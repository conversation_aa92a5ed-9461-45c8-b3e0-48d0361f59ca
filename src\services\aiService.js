import { googleAIApi<PERSON>ey, serverConfig } from '../firebaseConfig';
import axios from 'axios';

// Configuration
const config = {
  timeout: serverConfig.timeout || 15000,
  retryAttempts: serverConfig.retryAttempts || 2,
  retryDelay: 1000
};

// Create axios instance with default configuration
const aiClient = axios.create({
  timeout: config.timeout,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Retry utility for AI requests
const withRetry = async (fn, maxRetries = config.retryAttempts) => {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;

      // Don't retry on client errors (4xx) except for rate limits
      if (error.response && error.response.status >= 400 && error.response.status < 500) {
        if (error.response.status !== 429) {
          throw error;
        }
      }

      if (attempt < maxRetries) {
        const delay = config.retryDelay * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
};

// This service handles interactions with the Google Gemini API for AI-powered features

export const generateProfileSummary = async (skills, experiences) => {
  // Create a well-formatted prompt for the AI
  const prompt = `Based on the following key skills and experiences, generate a concise and professional profile summary (around 2-3 sentences):\n\n
Skills: ${skills}\n\n
Experiences:\n${experiences.map(exp => `- ${exp.title} at ${exp.company}: ${exp.summary}`).join('\n')}`;
  
  // Check if API key is available
  if (!googleAIApiKey) {
    console.warn('Missing Gemini API key. Using fallback mechanism.');
    return provideFallbackSummary(skills, experiences);
  }
  
  try {
    // Create the request payload for Gemini API
    const chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
    const payload = { contents: chatHistory };

    // Make the API request to Gemini with retry logic
    const response = await withRetry(async () => {
      return await aiClient.post(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${googleAIApiKey}`,
        payload
      );
    });
    
    // Extract and return the generated text
    if (response.data?.candidates?.length > 0 && 
        response.data.candidates[0]?.content?.parts?.length > 0) {
      return {
        success: true,
        summary: response.data.candidates[0].content.parts[0].text
      };
    } else {
      console.warn("Unexpected API response structure, using fallback");
      return provideFallbackSummary(skills, experiences);
    }
  } catch (error) {
    console.error("Error generating profile summary:", error);
    // Use fallback on error
    return provideFallbackSummary(skills, experiences, error.message);
  }
};

// Provide a fallback summary when the API is unavailable
function provideFallbackSummary(skills, experiences, errorMessage = null) {
  // Extract key information
  const skillsList = skills.split(',').map(skill => skill.trim()).filter(s => s);
  const topSkills = skillsList.slice(0, 3).join(', ');
  
  // Find most recent experience with non-empty title and company
  const validExperiences = experiences.filter(exp => exp.title && exp.company);
  const recentExp = validExperiences.length > 0 ? validExperiences[0] : null;
  
  let summary = '';
  
  if (errorMessage) {
    summary = `[Note: AI generation failed (${errorMessage}). Using template summary instead.] `;
  } else {
    summary = '[Note: Using template summary as AI is unavailable. Add your API key for AI-generated summaries.] ';
  }
  
  if (topSkills && recentExp) {
    summary += `Experienced professional with expertise in ${topSkills}` +
              ` and a background as ${recentExp.title} at ${recentExp.company}.` +
              ` Skilled in ${skillsList.length > 3 ? 'various areas including ' + topSkills + ' and more' : topSkills},` +
              ` with a proven track record of delivering results in ${validExperiences.length > 1 ? 'multiple roles' : 'the field'}.`;
  } else if (topSkills) {
    summary += `Professional with expertise in ${topSkills}` +
              ` and a passion for delivering high-quality results.` +
              ` Looking to leverage skills in ${skillsList.length > 3 ? 'various areas including ' + topSkills + ' and more' : topSkills}` +
              ` to contribute to organizational success.`;
  } else if (recentExp) {
    summary += `Experienced ${recentExp.title} with a background at ${recentExp.company},` +
              ` focusing on delivering exceptional results and driving success through dedicated efforts.`;
  } else {
    summary += `Detail-oriented professional with a strong work ethic and commitment to excellence.` +
              ` Seeking to leverage diverse skills and experience to contribute to organizational goals.`;
  }
  
  return {
    success: true,
    summary: summary,
    isAiFallback: true
  };
}

// Additional AI feature: Generate job application letter based on profile and job description
export const generateCoverLetter = async (profile, jobDescription) => {
  const prompt = `Generate a professional, personalized cover letter for a job application based on the following:
  
Job Description: ${jobDescription}

About the applicant:
- Name: ${profile.name}
- Skills: ${profile.keySkills}
- Experience: ${profile.experiences.map(exp => `${exp.title} at ${exp.company} (${exp.years}) - ${exp.summary}`).join('\n')}

The cover letter should be concise, professional, highlight relevant skills and experiences, and express genuine interest in the position.`;

  // Check if API key is set to a placeholder or empty
  if (!googleAIApiKey || googleAIApiKey === 'YOUR_GEMINI_API_KEY') {
    console.warn('Missing Gemini API key. Using fallback cover letter mechanism.');
    return provideFallbackCoverLetter(profile, jobDescription);
  }

  try {
    const chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
    const payload = { contents: chatHistory };
    
    const response = await axios.post(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${googleAIApiKey}`,
      payload,
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 15000 // Add timeout to prevent hanging requests
      }
    );
    
    if (response.data?.candidates?.length > 0 && 
        response.data.candidates[0]?.content?.parts?.length > 0) {
      return {
        success: true,
        coverLetter: response.data.candidates[0].content.parts[0].text
      };
    } else {
      console.warn("Unexpected API response structure, using fallback cover letter");
      return provideFallbackCoverLetter(profile, jobDescription);
    }
  } catch (error) {
    console.error("Error generating cover letter:", error);
    // Use fallback on error
    return provideFallbackCoverLetter(profile, jobDescription, error.message);
  }
};

// Provide a fallback cover letter when the API is unavailable
function provideFallbackCoverLetter(profile, jobDescription, errorMessage = null) {
  const { name, keySkills, experiences } = profile;
  
  // Extract key job information from description
  const jobTitleMatch = jobDescription.match(/(?:position|job|role)\s*(?:of|as|:)?\s*([^.,\n]+)/i);
  const jobTitle = jobTitleMatch ? jobTitleMatch[1].trim() : "the open position";
  
  // Extract company name if present
  const companyMatch = jobDescription.match(/(?:at|with|for)\s+([A-Z][a-zA-Z\s]+)(?:[.,]|\s+is|\s+are|\s+has|\s+have)/i);
  const company = companyMatch ? companyMatch[1].trim() : "your company";
  
  // Extract skills from profile
  const skills = keySkills.split(',').map(s => s.trim()).filter(s => s);
  const skillsText = skills.length > 0 ? 
    `${skills.slice(0, 3).join(', ')}${skills.length > 3 ? ', and other relevant skills' : ''}` : 
    'my professional skills';
  
  // Get most recent experience
  const validExperiences = experiences.filter(exp => exp.title && exp.company);
  const recentExp = validExperiences.length > 0 ? validExperiences[0] : null;
  
  // Create fallback letter
  let warning = '';
  if (errorMessage) {
    warning = `[Note: AI generation failed (${errorMessage}). Using template cover letter instead.]\n\n`;
  } else {
    warning = '[Note: Using template cover letter as AI is unavailable. Add your API key for AI-generated content.]\n\n';
  }
  
  const date = new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
  
  const letter = `${warning}${date}\n\nDear Hiring Manager,\n\n`+
    `I am writing to express my interest in ${jobTitle} at ${company}. With my background ${recentExp ? `as ${recentExp.title} at ${recentExp.company}` : 'in the field'}`+
    ` and expertise in ${skillsText}, I believe I would be a valuable addition to your team.\n\n`+
    `Based on the job description, my qualifications align well with your requirements. ${recentExp && recentExp.summary ? `During my time at ${recentExp.company}, ${recentExp.summary}` : ''}`+
    ` I am confident that these experiences have prepared me to excel in this role.\n\n`+
    `I am particularly interested in this position because it allows me to leverage my strengths in ${skillsText} while contributing to ${company}'s goals and objectives.`+
    ` I am excited about the opportunity to bring my skills and enthusiasm to your team.\n\n`+
    `Thank you for considering my application. I look forward to the possibility of discussing how I can contribute to ${company}.\n\n`+
    `Sincerely,\n\n${name || 'Your Name'}`;
  
  return {
    success: true,
    coverLetter: letter,
    isAiFallback: true
  };
}
