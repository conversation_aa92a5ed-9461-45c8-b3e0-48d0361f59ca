{"ast": null, "code": "/* global __initial_auth_token */import React,{useState,useEffect}from'react';import{initializeApp}from'firebase/app';import{getAuth,signInAnonymously,onAuthStateChanged,signInWithCustomToken}from'firebase/auth';import{getFirestore}from'firebase/firestore';// Import components\nimport UserProfile from'./components/UserProfile';import JobSites from'./components/JobSites';import JobSearch from'./components/JobSearch';import Footer from'./components/Footer';import LoadingScreen from'./components/LoadingScreen';import SettingsPage from'./components/SettingsPage';// Import the new SettingsPage component\n// Import Firebase configuration\nimport{firebaseConfig,appId}from'./firebaseConfig';// Initialize Firebase with error handling\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";let app,auth,db;try{app=initializeApp(firebaseConfig);auth=getAuth(app);db=getFirestore(app);}catch(error){console.error(\"Firebase initialization error:\",error);// We'll handle this error in the App component\n}// Main App Component\nfunction App(){const[currentPage,setCurrentPage]=useState('search');// 'profile', 'sites', 'search', 'settings'\nconst[userId,setUserId]=useState(null);const[isAuthReady,setIsAuthReady]=useState(false);const[isLoading,setIsLoading]=useState(true);const[firebaseError,setFirebaseError]=useState(null);// Firebase Auth State Listener\nuseEffect(()=>{// Check if Firebase was initialized properly\nif(!app||!auth||!db){setFirebaseError(\"Firebase initialization failed. Please check your configuration.\");setIsLoading(false);return;}// Set up auth state listener\nconsole.log('Initial auth token:',__initial_auth_token);const unsubscribe=onAuthStateChanged(auth,async user=>{if(user){setUserId(user.uid);setIsAuthReady(true);setIsLoading(false);}else{try{if(typeof __initial_auth_token!=='undefined'&&__initial_auth_token){try{await signInWithCustomToken(auth,__initial_auth_token);}catch(error){console.error(\"Custom token sign-in failed, falling back to anonymous sign-in: \",error);await signInAnonymously(auth);}}else{await signInAnonymously(auth);}}catch(error){console.error(\"Error signing in: \",error);setFirebaseError(\"Authentication error: \"+error.message);setIsAuthReady(true);setIsLoading(false);}}},error=>{console.error(\"Auth state change error:\",error);setFirebaseError(\"Authentication error: \"+error.message);setIsLoading(false);});return()=>unsubscribe();},[]);// Display loading screen\nif(isLoading){return/*#__PURE__*/_jsx(LoadingScreen,{message:\"Initializing Job Assistant...\"});}// Display Firebase configuration error\nif(firebaseError){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-slate-100 font-sans flex flex-col items-center justify-center p-8 text-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-red-500/20 border border-red-500 p-6 rounded-lg max-w-xl\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-red-400 mb-4\",children:\"Configuration Error\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-slate-300 mb-6\",children:firebaseError}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-slate-800/50 p-4 rounded-lg text-left\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-slate-400 mb-2\",children:\"To fix this:\"}),/*#__PURE__*/_jsxs(\"ol\",{className:\"list-decimal list-inside text-slate-400 space-y-2\",children:[/*#__PURE__*/_jsxs(\"li\",{children:[\"Open \",/*#__PURE__*/_jsx(\"code\",{className:\"bg-slate-700/50 px-2 py-1 rounded\",children:\"src/firebaseConfig.js\"})]}),/*#__PURE__*/_jsx(\"li\",{children:\"Replace the placeholder values with your actual Firebase project credentials\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Restart the application\"})]})]})]})});}// Display authentication screen\nif(!isAuthReady){return/*#__PURE__*/_jsx(LoadingScreen,{message:\"Authenticating...\"});}const renderPage=()=>{switch(currentPage){case'profile':return/*#__PURE__*/_jsx(UserProfile,{userId:userId,db:db,appId:appId});case'sites':return/*#__PURE__*/_jsx(JobSites,{userId:userId,db:db,appId:appId});case'search':return/*#__PURE__*/_jsx(JobSearch,{userId:userId,db:db,appId:appId});case'settings':// Add the new settings page case\nreturn/*#__PURE__*/_jsx(SettingsPage,{userId:userId,db:db,appId:appId});default:return/*#__PURE__*/_jsx(UserProfile,{userId:userId,db:db,appId:appId});}};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-slate-100 font-sans flex flex-col items-center p-4 sm:p-6 md:p-8 relative overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-0 right-0 w-96 h-96 bg-blue-500/10 rounded-full filter blur-3xl -z-10 animate-pulse\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute bottom-0 left-0 w-80 h-80 bg-purple-500/10 rounded-full filter blur-3xl -z-10 animate-pulse\",style:{animationDuration:'8s'}}),/*#__PURE__*/_jsxs(\"header\",{className:\"w-full max-w-4xl mb-10 text-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative inline-block\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-5xl sm:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-sky-400 via-indigo-400 to-purple-500 animate-gradient-x\",children:\"AI Job Assistant\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-gradient-to-r from-sky-400 to-purple-500 rounded-full\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-slate-300 mt-4 text-lg\",children:\"Your smart companion for navigating the job market.\"}),userId&&/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-slate-500 mt-2 bg-slate-800/50 inline-block px-2 py-1 rounded-full\",children:[\"User ID: \",userId]})]}),/*#__PURE__*/_jsx(\"nav\",{className:\"w-full max-w-md mb-10 bg-slate-800/70 backdrop-blur-lg p-2 rounded-xl shadow-[0_8px_30px_rgb(0,0,0,0.12)] border border-slate-700/50 flex justify-around transition-all duration-300 hover:shadow-[0_8px_30px_rgb(59,130,246,0.1)] animate-fade-in\",children:['profile','sites','search','settings'].map(page=>/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setCurrentPage(page),className:\"px-5 py-2.5 rounded-lg text-sm font-medium transition-all duration-300 ease-in-out relative overflow-hidden\\n              \".concat(currentPage===page?'bg-gradient-to-r from-sky-500 to-blue-600 text-white shadow-lg shadow-blue-500/20':'text-slate-300 hover:bg-slate-700/70 hover:text-sky-300',\"\\n            \"),children:[currentPage===page&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-white/20 animate-shine\"}),page.charAt(0).toUpperCase()+page.slice(1)]},page))}),/*#__PURE__*/_jsx(\"main\",{className:\"w-full max-w-4xl animate-fade-in-up\",children:renderPage()}),/*#__PURE__*/_jsx(Footer,{})]});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "initializeApp", "getAuth", "signInAnonymously", "onAuthStateChanged", "signInWithCustomToken", "getFirestore", "UserProfile", "JobSites", "JobSearch", "Footer", "LoadingScreen", "SettingsPage", "firebaseConfig", "appId", "jsx", "_jsx", "jsxs", "_jsxs", "app", "auth", "db", "error", "console", "App", "currentPage", "setCurrentPage", "userId", "setUserId", "isAuthReady", "setIsAuthReady", "isLoading", "setIsLoading", "firebaseError", "setFirebaseError", "log", "__initial_auth_token", "unsubscribe", "user", "uid", "message", "className", "children", "renderPage", "style", "animationDuration", "map", "page", "onClick", "concat", "char<PERSON>t", "toUpperCase", "slice"], "sources": ["C:/Users/<USER>/Documents/Repo/Auto_Job_Application/src/App.js"], "sourcesContent": ["/* global __initial_auth_token */\nimport React, { useState, useEffect } from 'react';\nimport { initializeApp } from 'firebase/app';\nimport { getAuth, signInAnonymously, onAuthStateChanged, signInWithCustomToken } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\n\n// Import components\nimport UserProfile from './components/UserProfile';\nimport JobSites from './components/JobSites';\nimport JobSearch from './components/JobSearch';\nimport Footer from './components/Footer';\nimport LoadingScreen from './components/LoadingScreen';\nimport SettingsPage from './components/SettingsPage'; // Import the new SettingsPage component\n\n// Import Firebase configuration\nimport { firebaseConfig, appId } from './firebaseConfig';\n\n// Initialize Firebase with error handling\nlet app, auth, db;\ntry {\n  app = initializeApp(firebaseConfig);\n  auth = getAuth(app);\n  db = getFirestore(app);\n} catch (error) {\n  console.error(\"Firebase initialization error:\", error);\n  // We'll handle this error in the App component\n}\n\n// Main App Component\nfunction App() {\n  const [currentPage, setCurrentPage] = useState('search'); // 'profile', 'sites', 'search', 'settings'\n  const [userId, setUserId] = useState(null);\n  const [isAuthReady, setIsAuthReady] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [firebaseError, setFirebaseError] = useState(null);\n\n  // Firebase Auth State Listener\n  useEffect(() => {\n    // Check if Firebase was initialized properly\n    if (!app || !auth || !db) {\n      setFirebaseError(\"Firebase initialization failed. Please check your configuration.\");\n      setIsLoading(false);\n      return;\n    }\n\n    // Set up auth state listener\n    console.log('Initial auth token:', __initial_auth_token);\n    const unsubscribe = onAuthStateChanged(auth, async (user) => {\n      if (user) {\n        setUserId(user.uid);\n        setIsAuthReady(true);\n        setIsLoading(false);\n      } else {\n        try {\n          if (typeof __initial_auth_token !== 'undefined' && __initial_auth_token) {\n            try {\n              await signInWithCustomToken(auth, __initial_auth_token);\n            } catch (error) {\n              console.error(\"Custom token sign-in failed, falling back to anonymous sign-in: \", error);\n              await signInAnonymously(auth);\n            }\n          } else {\n            await signInAnonymously(auth);\n          }\n        } catch (error) {\n          console.error(\"Error signing in: \", error);\n          setFirebaseError(\"Authentication error: \" + error.message);\n          setIsAuthReady(true);\n          setIsLoading(false);\n        }\n      }\n    }, (error) => {\n      console.error(\"Auth state change error:\", error);\n      setFirebaseError(\"Authentication error: \" + error.message);\n      setIsLoading(false);\n    });\n    \n    return () => unsubscribe();\n  }, []);\n\n  // Display loading screen\n  if (isLoading) {\n    return <LoadingScreen message=\"Initializing Job Assistant...\" />;\n  }\n  \n  // Display Firebase configuration error\n  if (firebaseError) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-slate-100 font-sans flex flex-col items-center justify-center p-8 text-center\">\n        <div className=\"bg-red-500/20 border border-red-500 p-6 rounded-lg max-w-xl\">\n          <h2 className=\"text-2xl font-bold text-red-400 mb-4\">Configuration Error</h2>\n          <p className=\"text-slate-300 mb-6\">{firebaseError}</p>\n          <div className=\"bg-slate-800/50 p-4 rounded-lg text-left\">\n            <p className=\"text-slate-400 mb-2\">To fix this:</p>\n            <ol className=\"list-decimal list-inside text-slate-400 space-y-2\">\n              <li>Open <code className=\"bg-slate-700/50 px-2 py-1 rounded\">src/firebaseConfig.js</code></li>\n              <li>Replace the placeholder values with your actual Firebase project credentials</li>\n              <li>Restart the application</li>\n            </ol>\n          </div>\n        </div>\n      </div>\n    );\n  }\n  \n  // Display authentication screen\n  if (!isAuthReady) {\n    return <LoadingScreen message=\"Authenticating...\" />;\n  }\n\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'profile':\n        return <UserProfile userId={userId} db={db} appId={appId} />;\n      case 'sites':\n        return <JobSites userId={userId} db={db} appId={appId} />;\n      case 'search':\n        return <JobSearch userId={userId} db={db} appId={appId} />;\n      case 'settings': // Add the new settings page case\n        return <SettingsPage userId={userId} db={db} appId={appId} />;\n      default:\n        return <UserProfile userId={userId} db={db} appId={appId} />;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-slate-100 font-sans flex flex-col items-center p-4 sm:p-6 md:p-8 relative overflow-hidden\">\n      {/* Background decoration elements */}\n      <div className=\"absolute top-0 right-0 w-96 h-96 bg-blue-500/10 rounded-full filter blur-3xl -z-10 animate-pulse\"></div>\n      <div className=\"absolute bottom-0 left-0 w-80 h-80 bg-purple-500/10 rounded-full filter blur-3xl -z-10 animate-pulse\" style={{animationDuration: '8s'}}></div>\n      \n      <header className=\"w-full max-w-4xl mb-10 text-center\">\n        <div className=\"relative inline-block\">\n          <h1 className=\"text-5xl sm:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-sky-400 via-indigo-400 to-purple-500 animate-gradient-x\">\n            AI Job Assistant\n          </h1>\n          <div className=\"absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-gradient-to-r from-sky-400 to-purple-500 rounded-full\"></div>\n        </div>\n        <p className=\"text-slate-300 mt-4 text-lg\">Your smart companion for navigating the job market.</p>\n        {userId && <p className=\"text-xs text-slate-500 mt-2 bg-slate-800/50 inline-block px-2 py-1 rounded-full\">User ID: {userId}</p>}\n      </header>\n\n      <nav className=\"w-full max-w-md mb-10 bg-slate-800/70 backdrop-blur-lg p-2 rounded-xl shadow-[0_8px_30px_rgb(0,0,0,0.12)] border border-slate-700/50 flex justify-around transition-all duration-300 hover:shadow-[0_8px_30px_rgb(59,130,246,0.1)] animate-fade-in\">\n        {['profile', 'sites', 'search', 'settings'].map((page) => (\n          <button\n            key={page}\n            onClick={() => setCurrentPage(page)}\n            className={`px-5 py-2.5 rounded-lg text-sm font-medium transition-all duration-300 ease-in-out relative overflow-hidden\n              ${currentPage === page\n                ? 'bg-gradient-to-r from-sky-500 to-blue-600 text-white shadow-lg shadow-blue-500/20'\n                : 'text-slate-300 hover:bg-slate-700/70 hover:text-sky-300'\n              }\n            `}\n          >\n            {currentPage === page && (\n              <div className=\"absolute inset-0 bg-white/20 animate-shine\"></div>\n            )}\n            {page.charAt(0).toUpperCase() + page.slice(1)}\n          </button>\n        ))}\n      </nav>\n\n      <main className=\"w-full max-w-4xl animate-fade-in-up\">\n        {renderPage()}\n      </main>\n      \n      <Footer />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,iCACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,aAAa,KAAQ,cAAc,CAC5C,OAASC,OAAO,CAAEC,iBAAiB,CAAEC,kBAAkB,CAAEC,qBAAqB,KAAQ,eAAe,CACrG,OAASC,YAAY,KAAQ,oBAAoB,CAEjD;AACA,MAAO,CAAAC,WAAW,KAAM,0BAA0B,CAClD,MAAO,CAAAC,QAAQ,KAAM,uBAAuB,CAC5C,MAAO,CAAAC,SAAS,KAAM,wBAAwB,CAC9C,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,MAAO,CAAAC,YAAY,KAAM,2BAA2B,CAAE;AAEtD;AACA,OAASC,cAAc,CAAEC,KAAK,KAAQ,kBAAkB,CAExD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,GAAI,CAAAC,GAAG,CAAEC,IAAI,CAAEC,EAAE,CACjB,GAAI,CACFF,GAAG,CAAGlB,aAAa,CAACY,cAAc,CAAC,CACnCO,IAAI,CAAGlB,OAAO,CAACiB,GAAG,CAAC,CACnBE,EAAE,CAAGf,YAAY,CAACa,GAAG,CAAC,CACxB,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD;AACF,CAEA;AACA,QAAS,CAAAE,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG3B,QAAQ,CAAC,QAAQ,CAAC,CAAE;AAC1D,KAAM,CAAC4B,MAAM,CAAEC,SAAS,CAAC,CAAG7B,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAAC8B,WAAW,CAAEC,cAAc,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACgC,SAAS,CAAEC,YAAY,CAAC,CAAGjC,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACkC,aAAa,CAAEC,gBAAgB,CAAC,CAAGnC,QAAQ,CAAC,IAAI,CAAC,CAExD;AACAC,SAAS,CAAC,IAAM,CACd;AACA,GAAI,CAACmB,GAAG,EAAI,CAACC,IAAI,EAAI,CAACC,EAAE,CAAE,CACxBa,gBAAgB,CAAC,kEAAkE,CAAC,CACpFF,YAAY,CAAC,KAAK,CAAC,CACnB,OACF,CAEA;AACAT,OAAO,CAACY,GAAG,CAAC,qBAAqB,CAAEC,oBAAoB,CAAC,CACxD,KAAM,CAAAC,WAAW,CAAGjC,kBAAkB,CAACgB,IAAI,CAAE,KAAO,CAAAkB,IAAI,EAAK,CAC3D,GAAIA,IAAI,CAAE,CACRV,SAAS,CAACU,IAAI,CAACC,GAAG,CAAC,CACnBT,cAAc,CAAC,IAAI,CAAC,CACpBE,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACL,GAAI,CACF,GAAI,MAAO,CAAAI,oBAAoB,GAAK,WAAW,EAAIA,oBAAoB,CAAE,CACvE,GAAI,CACF,KAAM,CAAA/B,qBAAqB,CAACe,IAAI,CAAEgB,oBAAoB,CAAC,CACzD,CAAE,MAAOd,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,kEAAkE,CAAEA,KAAK,CAAC,CACxF,KAAM,CAAAnB,iBAAiB,CAACiB,IAAI,CAAC,CAC/B,CACF,CAAC,IAAM,CACL,KAAM,CAAAjB,iBAAiB,CAACiB,IAAI,CAAC,CAC/B,CACF,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1CY,gBAAgB,CAAC,wBAAwB,CAAGZ,KAAK,CAACkB,OAAO,CAAC,CAC1DV,cAAc,CAAC,IAAI,CAAC,CACpBE,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CACF,CAAC,CAAGV,KAAK,EAAK,CACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDY,gBAAgB,CAAC,wBAAwB,CAAGZ,KAAK,CAACkB,OAAO,CAAC,CAC1DR,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAC,CAEF,MAAO,IAAMK,WAAW,CAAC,CAAC,CAC5B,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,GAAIN,SAAS,CAAE,CACb,mBAAOf,IAAA,CAACL,aAAa,EAAC6B,OAAO,CAAC,+BAA+B,CAAE,CAAC,CAClE,CAEA;AACA,GAAIP,aAAa,CAAE,CACjB,mBACEjB,IAAA,QAAKyB,SAAS,CAAC,6JAA6J,CAAAC,QAAA,cAC1KxB,KAAA,QAAKuB,SAAS,CAAC,6DAA6D,CAAAC,QAAA,eAC1E1B,IAAA,OAAIyB,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAC7E1B,IAAA,MAAGyB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAET,aAAa,CAAI,CAAC,cACtDf,KAAA,QAAKuB,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eACvD1B,IAAA,MAAGyB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,cAAY,CAAG,CAAC,cACnDxB,KAAA,OAAIuB,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAC/DxB,KAAA,OAAAwB,QAAA,EAAI,OAAK,cAAA1B,IAAA,SAAMyB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,uBAAqB,CAAM,CAAC,EAAI,CAAC,cAC9F1B,IAAA,OAAA0B,QAAA,CAAI,8EAA4E,CAAI,CAAC,cACrF1B,IAAA,OAAA0B,QAAA,CAAI,yBAAuB,CAAI,CAAC,EAC9B,CAAC,EACF,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAEA;AACA,GAAI,CAACb,WAAW,CAAE,CAChB,mBAAOb,IAAA,CAACL,aAAa,EAAC6B,OAAO,CAAC,mBAAmB,CAAE,CAAC,CACtD,CAEA,KAAM,CAAAG,UAAU,CAAGA,CAAA,GAAM,CACvB,OAAQlB,WAAW,EACjB,IAAK,SAAS,CACZ,mBAAOT,IAAA,CAACT,WAAW,EAACoB,MAAM,CAAEA,MAAO,CAACN,EAAE,CAAEA,EAAG,CAACP,KAAK,CAAEA,KAAM,CAAE,CAAC,CAC9D,IAAK,OAAO,CACV,mBAAOE,IAAA,CAACR,QAAQ,EAACmB,MAAM,CAAEA,MAAO,CAACN,EAAE,CAAEA,EAAG,CAACP,KAAK,CAAEA,KAAM,CAAE,CAAC,CAC3D,IAAK,QAAQ,CACX,mBAAOE,IAAA,CAACP,SAAS,EAACkB,MAAM,CAAEA,MAAO,CAACN,EAAE,CAAEA,EAAG,CAACP,KAAK,CAAEA,KAAM,CAAE,CAAC,CAC5D,IAAK,UAAU,CAAE;AACf,mBAAOE,IAAA,CAACJ,YAAY,EAACe,MAAM,CAAEA,MAAO,CAACN,EAAE,CAAEA,EAAG,CAACP,KAAK,CAAEA,KAAM,CAAE,CAAC,CAC/D,QACE,mBAAOE,IAAA,CAACT,WAAW,EAACoB,MAAM,CAAEA,MAAO,CAACN,EAAE,CAAEA,EAAG,CAACP,KAAK,CAAEA,KAAM,CAAE,CAAC,CAChE,CACF,CAAC,CAED,mBACEI,KAAA,QAAKuB,SAAS,CAAC,yKAAyK,CAAAC,QAAA,eAEtL1B,IAAA,QAAKyB,SAAS,CAAC,kGAAkG,CAAM,CAAC,cACxHzB,IAAA,QAAKyB,SAAS,CAAC,sGAAsG,CAACG,KAAK,CAAE,CAACC,iBAAiB,CAAE,IAAI,CAAE,CAAM,CAAC,cAE9J3B,KAAA,WAAQuB,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACpDxB,KAAA,QAAKuB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC1B,IAAA,OAAIyB,SAAS,CAAC,4IAA4I,CAAAC,QAAA,CAAC,kBAE3J,CAAI,CAAC,cACL1B,IAAA,QAAKyB,SAAS,CAAC,0HAA0H,CAAM,CAAC,EAC7I,CAAC,cACNzB,IAAA,MAAGyB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,qDAAmD,CAAG,CAAC,CACjGf,MAAM,eAAIT,KAAA,MAAGuB,SAAS,CAAC,iFAAiF,CAAAC,QAAA,EAAC,WAAS,CAACf,MAAM,EAAI,CAAC,EACzH,CAAC,cAETX,IAAA,QAAKyB,SAAS,CAAC,oPAAoP,CAAAC,QAAA,CAChQ,CAAC,SAAS,CAAE,OAAO,CAAE,QAAQ,CAAE,UAAU,CAAC,CAACI,GAAG,CAAEC,IAAI,eACnD7B,KAAA,WAEE8B,OAAO,CAAEA,CAAA,GAAMtB,cAAc,CAACqB,IAAI,CAAE,CACpCN,SAAS,+HAAAQ,MAAA,CACLxB,WAAW,GAAKsB,IAAI,CAClB,mFAAmF,CACnF,yDAAyD,kBAE7D,CAAAL,QAAA,EAEDjB,WAAW,GAAKsB,IAAI,eACnB/B,IAAA,QAAKyB,SAAS,CAAC,4CAA4C,CAAM,CAClE,CACAM,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGJ,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC,GAZxCL,IAaC,CACT,CAAC,CACC,CAAC,cAEN/B,IAAA,SAAMyB,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAClDC,UAAU,CAAC,CAAC,CACT,CAAC,cAEP3B,IAAA,CAACN,MAAM,GAAE,CAAC,EACP,CAAC,CAEV,CAEA,cAAe,CAAAc,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}