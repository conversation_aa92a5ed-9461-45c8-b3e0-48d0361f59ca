{"ast": null, "code": "/**\n * lucide-react v0.279.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ClipboardCopy = createLucideIcon(\"ClipboardCopy\", [[\"rect\", {\n  width: \"8\",\n  height: \"4\",\n  x: \"8\",\n  y: \"2\",\n  rx: \"1\",\n  ry: \"1\",\n  key: \"tgr4d6\"\n}], [\"path\", {\n  d: \"M8 4H6a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-2\",\n  key: \"4jdomd\"\n}], [\"path\", {\n  d: \"M16 4h2a2 2 0 0 1 2 2v4\",\n  key: \"3hqy98\"\n}], [\"path\", {\n  d: \"M21 14H11\",\n  key: \"1bme5i\"\n}], [\"path\", {\n  d: \"m15 10-4 4 4 4\",\n  key: \"5dvupr\"\n}]]);\nexport { ClipboardCopy as default };", "map": {"version": 3, "names": ["ClipboardCopy", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "d"], "sources": ["C:\\Users\\<USER>\\Documents\\Repo\\Auto_Job_Application\\node_modules\\lucide-react\\src\\icons\\clipboard-copy.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ClipboardCopy\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iOCIgaGVpZ2h0PSI0IiB4PSI4IiB5PSIyIiByeD0iMSIgcnk9IjEiIC8+CiAgPHBhdGggZD0iTTggNEg2YTIgMiAwIDAgMC0yIDJ2MTRhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0ydi0yIiAvPgogIDxwYXRoIGQ9Ik0xNiA0aDJhMiAyIDAgMCAxIDIgMnY0IiAvPgogIDxwYXRoIGQ9Ik0yMSAxNEgxMSIgLz4KICA8cGF0aCBkPSJtMTUgMTAtNCA0IDQgNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/clipboard-copy\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ClipboardCopy = createLucideIcon('ClipboardCopy', [\n  [\n    'rect',\n    {\n      width: '8',\n      height: '4',\n      x: '8',\n      y: '2',\n      rx: '1',\n      ry: '1',\n      key: 'tgr4d6',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M8 4H6a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-2',\n      key: '4jdomd',\n    },\n  ],\n  ['path', { d: 'M16 4h2a2 2 0 0 1 2 2v4', key: '3hqy98' }],\n  ['path', { d: 'M21 14H11', key: '1bme5i' }],\n  ['path', { d: 'm15 10-4 4 4 4', key: '5dvupr' }],\n]);\n\nexport default ClipboardCopy;\n"], "mappings": ";;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CACE,QACA;EACEC,KAAO;EACPC,MAAQ;EACRC,CAAG;EACHC,CAAG;EACHC,EAAI;EACJC,EAAI;EACJC,GAAK;AACP,EACF,EACA,CACE,QACA;EACEC,CAAG;EACHD,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,EAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}