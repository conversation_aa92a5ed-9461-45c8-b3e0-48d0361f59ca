/**
 * Firebase Configuration
 *
 * This configuration uses environment variables for security.
 * Create a .env file in your project root with the following variables:
 *
 * REACT_APP_FIREBASE_API_KEY=your_api_key_here
 * REACT_APP_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
 * REACT_APP_FIREBASE_PROJECT_ID=your_project_id
 * REACT_APP_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
 * REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
 * REACT_APP_FIREBASE_APP_ID=your_app_id
 * REACT_APP_GOOGLE_AI_API_KEY=your_gemini_api_key
 * REACT_APP_APP_ID=job-assistant-app-v1
 *
 * For development, you can use the fallback values below.
 */

// Validate required environment variables
const requiredEnvVars = [
  'REACT_APP_FIREBASE_API_KEY',
  'REACT_APP_FIREBASE_AUTH_DOMAIN',
  'REACT_APP_FIREBASE_PROJECT_ID',
  'REACT_APP_FIREBASE_STORAGE_BUCKET',
  'REACT_APP_FIREBASE_MESSAGING_SENDER_ID',
  'REACT_APP_FIREBASE_APP_ID'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0 && process.env.NODE_ENV === 'production') {
  console.error('Missing required environment variables:', missingVars);
  throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
}

// Firebase configuration with environment variables and fallbacks for development
export const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY || "demo-api-key",
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN || "demo-project.firebaseapp.com",
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID || "demo-project",
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET || "demo-project.appspot.com",
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID || "123456789",
  appId: process.env.REACT_APP_FIREBASE_APP_ID || "1:123456789:web:demo"
};

/**
 * Global App ID
 * This is used to organize and separate user data in Firestore
 */
export const appId = process.env.REACT_APP_APP_ID || "job-assistant-app-v1";

/**
 * Google AI API Key for Gemini API
 * This is optional - the app will work without it using fallback summaries
 */
export const googleAIApiKey = process.env.REACT_APP_GOOGLE_AI_API_KEY || null;

/**
 * Server configuration
 */
export const serverConfig = {
  url: process.env.REACT_APP_SERVER_URL || 'http://localhost:5000',
  timeout: parseInt(process.env.REACT_APP_REQUEST_TIMEOUT) || 30000,
  retryAttempts: parseInt(process.env.REACT_APP_RETRY_ATTEMPTS) || 3
};

/**
 * Check if we're in development mode with demo configuration
 */
export const isDemoMode = !process.env.REACT_APP_FIREBASE_API_KEY ||
                          process.env.REACT_APP_FIREBASE_API_KEY === "demo-api-key";
