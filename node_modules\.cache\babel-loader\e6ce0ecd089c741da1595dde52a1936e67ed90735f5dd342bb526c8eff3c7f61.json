{"ast": null, "code": "/**\n * @license React\n * react-jsx-runtime.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';\n\nvar f = require(\"react\"),\n  k = Symbol.for(\"react.element\"),\n  l = Symbol.for(\"react.fragment\"),\n  m = Object.prototype.hasOwnProperty,\n  n = f.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,\n  p = {\n    key: !0,\n    ref: !0,\n    __self: !0,\n    __source: !0\n  };\nfunction q(c, a, g) {\n  var b,\n    d = {},\n    e = null,\n    h = null;\n  void 0 !== g && (e = \"\" + g);\n  void 0 !== a.key && (e = \"\" + a.key);\n  void 0 !== a.ref && (h = a.ref);\n  for (b in a) m.call(a, b) && !p.hasOwnProperty(b) && (d[b] = a[b]);\n  if (c && c.defaultProps) for (b in a = c.defaultProps, a) void 0 === d[b] && (d[b] = a[b]);\n  return {\n    $$typeof: k,\n    type: c,\n    key: e,\n    ref: h,\n    props: d,\n    _owner: n.current\n  };\n}\nexports.Fragment = l;\nexports.jsx = q;\nexports.jsxs = q;", "map": {"version": 3, "names": ["f", "require", "k", "Symbol", "for", "l", "m", "Object", "prototype", "hasOwnProperty", "n", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "ReactCurrentOwner", "p", "key", "ref", "__self", "__source", "q", "c", "a", "g", "b", "d", "e", "h", "call", "defaultProps", "$$typeof", "type", "props", "_owner", "current", "exports", "Fragment", "jsx", "jsxs"], "sources": ["C:/Users/<USER>/Documents/Repo/Auto_Job_Application/node_modules/react/cjs/react-jsx-runtime.production.min.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var f=require(\"react\"),k=Symbol.for(\"react.element\"),l=Symbol.for(\"react.fragment\"),m=Object.prototype.hasOwnProperty,n=f.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,p={key:!0,ref:!0,__self:!0,__source:!0};\nfunction q(c,a,g){var b,d={},e=null,h=null;void 0!==g&&(e=\"\"+g);void 0!==a.key&&(e=\"\"+a.key);void 0!==a.ref&&(h=a.ref);for(b in a)m.call(a,b)&&!p.hasOwnProperty(b)&&(d[b]=a[b]);if(c&&c.defaultProps)for(b in a=c.defaultProps,a)void 0===d[b]&&(d[b]=a[b]);return{$$typeof:k,type:c,key:e,ref:h,props:d,_owner:n.current}}exports.Fragment=l;exports.jsx=q;exports.jsxs=q;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAAC,IAAIA,CAAC,GAACC,OAAO,CAAC,OAAO,CAAC;EAACC,CAAC,GAACC,MAAM,CAACC,GAAG,CAAC,eAAe,CAAC;EAACC,CAAC,GAACF,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;EAACE,CAAC,GAACC,MAAM,CAACC,SAAS,CAACC,cAAc;EAACC,CAAC,GAACV,CAAC,CAACW,kDAAkD,CAACC,iBAAiB;EAACC,CAAC,GAAC;IAACC,GAAG,EAAC,CAAC,CAAC;IAACC,GAAG,EAAC,CAAC,CAAC;IAACC,MAAM,EAAC,CAAC,CAAC;IAACC,QAAQ,EAAC,CAAC;EAAC,CAAC;AACnP,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC;IAACC,CAAC,GAAC,CAAC,CAAC;IAACC,CAAC,GAAC,IAAI;IAACC,CAAC,GAAC,IAAI;EAAC,KAAK,CAAC,KAAGJ,CAAC,KAAGG,CAAC,GAAC,EAAE,GAACH,CAAC,CAAC;EAAC,KAAK,CAAC,KAAGD,CAAC,CAACN,GAAG,KAAGU,CAAC,GAAC,EAAE,GAACJ,CAAC,CAACN,GAAG,CAAC;EAAC,KAAK,CAAC,KAAGM,CAAC,CAACL,GAAG,KAAGU,CAAC,GAACL,CAAC,CAACL,GAAG,CAAC;EAAC,KAAIO,CAAC,IAAIF,CAAC,EAACd,CAAC,CAACoB,IAAI,CAACN,CAAC,EAACE,CAAC,CAAC,IAAE,CAACT,CAAC,CAACJ,cAAc,CAACa,CAAC,CAAC,KAAGC,CAAC,CAACD,CAAC,CAAC,GAACF,CAAC,CAACE,CAAC,CAAC,CAAC;EAAC,IAAGH,CAAC,IAAEA,CAAC,CAACQ,YAAY,EAAC,KAAIL,CAAC,IAAIF,CAAC,GAACD,CAAC,CAACQ,YAAY,EAACP,CAAC,EAAC,KAAK,CAAC,KAAGG,CAAC,CAACD,CAAC,CAAC,KAAGC,CAAC,CAACD,CAAC,CAAC,GAACF,CAAC,CAACE,CAAC,CAAC,CAAC;EAAC,OAAM;IAACM,QAAQ,EAAC1B,CAAC;IAAC2B,IAAI,EAACV,CAAC;IAACL,GAAG,EAACU,CAAC;IAACT,GAAG,EAACU,CAAC;IAACK,KAAK,EAACP,CAAC;IAACQ,MAAM,EAACrB,CAAC,CAACsB;EAAO,CAAC;AAAA;AAACC,OAAO,CAACC,QAAQ,GAAC7B,CAAC;AAAC4B,OAAO,CAACE,GAAG,GAACjB,CAAC;AAACe,OAAO,CAACG,IAAI,GAAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}