{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Documents/Repo/Auto_Job_Application/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{doc,getDoc,setDoc}from'firebase/firestore';import{Settings,Bell,Mail,Save,XCircle,CheckCircle,Search}from'lucide-react';import{LoadingSpinner}from'./UIComponents';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SettingsPage=_ref=>{let{userId,db,appId}=_ref;const[settings,setSettings]=useState({emailNotifications:true,pushNotifications:false,darkMode:true,jobAlertFrequency:'daily',maxDistance:50});const[loading,setLoading]=useState(true);const[saving,setSaving]=useState(false);const[error,setError]=useState(null);const[success,setSuccess]=useState(false);useEffect(()=>{const fetchSettings=async()=>{if(!userId||!db||!appId){setLoading(false);return;}try{const docRef=doc(db,'artifacts',appId,'users',userId,'preferences','settings');const docSnap=await getDoc(docRef);if(docSnap.exists()){setSettings(prev=>_objectSpread(_objectSpread({},prev),docSnap.data()));}}catch(err){console.error(\"Error fetching settings:\",err);setError(\"Failed to load settings. Please try again.\");}finally{setLoading(false);}};fetchSettings();},[userId,db,appId]);const handleChange=e=>{const{name,value,type,checked}=e.target;const val=type==='checkbox'?checked:type==='number'?parseInt(value,10):value;setSettings(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:val}));};const handleSubmit=async e=>{e.preventDefault();setSaving(true);setError(null);setSuccess(false);if(!userId||!db||!appId){setError(\"User not authenticated or database not available.\");setSaving(false);return;}try{const docRef=doc(db,'artifacts',appId,'users',userId,'preferences','settings');await setDoc(docRef,settings,{merge:true});setSuccess(true);setTimeout(()=>setSuccess(false),3000);// Hide success message after 3 seconds\n}catch(err){console.error(\"Error saving settings:\",err);setError(\"Failed to save settings. Please try again.\");}finally{setSaving(false);}};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center items-center h-48\",children:/*#__PURE__*/_jsx(LoadingSpinner,{})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-slate-800/70 backdrop-blur-lg p-6 rounded-xl shadow-[0_8px_30px_rgb(0,0,0,0.12)] border border-slate-700/50 w-full max-w-2xl mx-auto animate-fade-in-up\",children:[/*#__PURE__*/_jsxs(\"h2\",{className:\"text-3xl font-bold text-sky-400 mb-6 flex items-center\",children:[/*#__PURE__*/_jsx(Settings,{className:\"mr-3\",size:30}),\"Settings\"]}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-red-500/20 border border-red-500 text-red-400 p-3 rounded-lg mb-4 flex items-center\",children:[/*#__PURE__*/_jsx(XCircle,{className:\"mr-2\",size:20}),error]}),success&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-green-500/20 border border-green-500 text-green-400 p-3 rounded-lg mb-4 flex items-center\",children:[/*#__PURE__*/_jsx(CheckCircle,{className:\"mr-2\",size:20}),\"Settings saved successfully!\"]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-slate-700/50 p-4 rounded-lg border border-slate-600\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-xl font-semibold text-slate-200 mb-4 flex items-center\",children:[/*#__PURE__*/_jsx(Bell,{className:\"mr-2\",size:22}),\" Notifications\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-3\",children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"emailNotifications\",className:\"text-slate-300 flex items-center\",children:[/*#__PURE__*/_jsx(Mail,{className:\"mr-2\",size:18}),\" Email Notifications\"]}),/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",id:\"emailNotifications\",name:\"emailNotifications\",checked:settings.emailNotifications,onChange:handleChange,className:\"form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"pushNotifications\",className:\"text-slate-300 flex items-center\",children:[/*#__PURE__*/_jsx(Bell,{className:\"mr-2\",size:18}),\" Push Notifications\"]}),/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",id:\"pushNotifications\",name:\"pushNotifications\",checked:settings.pushNotifications,onChange:handleChange,className:\"form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-slate-700/50 p-4 rounded-lg border border-slate-600\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-xl font-semibold text-slate-200 mb-4 flex items-center\",children:[/*#__PURE__*/_jsx(Settings,{className:\"mr-2\",size:22}),\" Display\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"darkMode\",className:\"text-slate-300 flex items-center\",children:\"Dark Mode\"}),/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",id:\"darkMode\",name:\"darkMode\",checked:settings.darkMode,onChange:handleChange,className:\"form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-slate-700/50 p-4 rounded-lg border border-slate-600\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-xl font-semibold text-slate-200 mb-4 flex items-center\",children:[/*#__PURE__*/_jsx(Search,{className:\"mr-2\",size:22}),\" Job Search Preferences\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"jobAlertFrequency\",className:\"block text-slate-300 text-sm font-medium mb-2\",children:\"Job Alert Frequency\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"jobAlertFrequency\",name:\"jobAlertFrequency\",value:settings.jobAlertFrequency,onChange:handleChange,className:\"mt-1 block w-full pl-3 pr-10 py-2 text-base bg-slate-900 border border-slate-600 focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm rounded-md text-slate-200\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"daily\",children:\"Daily\"}),/*#__PURE__*/_jsx(\"option\",{value:\"weekly\",children:\"Weekly\"}),/*#__PURE__*/_jsx(\"option\",{value:\"monthly\",children:\"Monthly\"}),/*#__PURE__*/_jsx(\"option\",{value:\"never\",children:\"Never\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"maxDistance\",className:\"block text-slate-300 text-sm font-medium mb-2\",children:\"Maximum Job Distance (miles)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",id:\"maxDistance\",name:\"maxDistance\",value:settings.maxDistance,onChange:handleChange,min:\"1\",max:\"500\",className:\"mt-1 block w-full pl-3 pr-3 py-2 text-base bg-slate-900 border border-slate-600 focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm rounded-md text-slate-200\"})]})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"w-full bg-gradient-to-r from-sky-500 to-blue-600 text-white font-bold py-3 px-4 rounded-lg shadow-lg hover:from-sky-600 hover:to-blue-700 transition-all duration-300 ease-in-out flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed\",disabled:saving,children:saving?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"svg\",{className:\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",children:[/*#__PURE__*/_jsx(\"circle\",{className:\"opacity-25\",cx:\"12\",cy:\"12\",r:\"10\",stroke:\"currentColor\",strokeWidth:\"4\"}),/*#__PURE__*/_jsx(\"path\",{className:\"opacity-75\",fill:\"currentColor\",d:\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"})]}),\"Saving...\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Save,{className:\"mr-2\",size:20}),\"Save Settings\"]})})]})]});};export default SettingsPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "doc", "getDoc", "setDoc", "Settings", "Bell", "Mail", "Save", "XCircle", "CheckCircle", "Search", "LoadingSpinner", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "SettingsPage", "_ref", "userId", "db", "appId", "settings", "setSettings", "emailNotifications", "pushNotifications", "darkMode", "jobAlertFrequency", "maxDistance", "loading", "setLoading", "saving", "setSaving", "error", "setError", "success", "setSuccess", "fetchSettings", "doc<PERSON>ef", "docSnap", "exists", "prev", "_objectSpread", "data", "err", "console", "handleChange", "e", "name", "value", "type", "checked", "target", "val", "parseInt", "handleSubmit", "preventDefault", "merge", "setTimeout", "className", "children", "size", "onSubmit", "htmlFor", "id", "onChange", "min", "max", "disabled", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "d"], "sources": ["C:/Users/<USER>/Documents/Repo/Auto_Job_Application/src/components/SettingsPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { doc, getDoc, setDoc } from 'firebase/firestore';\r\nimport { Settings, Bell, Mail, Save, XCircle, CheckCircle, Search } from 'lucide-react';\r\nimport { LoadingSpinner } from './UIComponents';\r\n\r\nconst SettingsPage = ({ userId, db, appId }) => {\r\n  const [settings, setSettings] = useState({\r\n    emailNotifications: true,\r\n    pushNotifications: false,\r\n    darkMode: true,\r\n    jobAlertFrequency: 'daily',\r\n    maxDistance: 50,\r\n  });\r\n  const [loading, setLoading] = useState(true);\r\n  const [saving, setSaving] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const [success, setSuccess] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const fetchSettings = async () => {\r\n      if (!userId || !db || !appId) {\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      try {\r\n        const docRef = doc(db, 'artifacts', appId, 'users', userId, 'preferences', 'settings');\r\n        const docSnap = await getDoc(docRef);\r\n        if (docSnap.exists()) {\r\n          setSettings(prev => ({ ...prev, ...docSnap.data() }));\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error fetching settings:\", err);\r\n        setError(\"Failed to load settings. Please try again.\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchSettings();\r\n  }, [userId, db, appId]);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    const val = type === 'checkbox' ? checked : type === 'number' ? parseInt(value, 10) : value;\r\n    setSettings(prev => ({\r\n      ...prev,\r\n      [name]: val\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setSaving(true);\r\n    setError(null);\r\n    setSuccess(false);\r\n\r\n    if (!userId || !db || !appId) {\r\n      setError(\"User not authenticated or database not available.\");\r\n      setSaving(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const docRef = doc(db, 'artifacts', appId, 'users', userId, 'preferences', 'settings');\r\n      await setDoc(docRef, settings, { merge: true });\r\n      setSuccess(true);\r\n      setTimeout(() => setSuccess(false), 3000); // Hide success message after 3 seconds\r\n    } catch (err) {\r\n      console.error(\"Error saving settings:\", err);\r\n      setError(\"Failed to save settings. Please try again.\");\r\n    } finally {\r\n      setSaving(false);\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center h-48\">\r\n        <LoadingSpinner />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-slate-800/70 backdrop-blur-lg p-6 rounded-xl shadow-[0_8px_30px_rgb(0,0,0,0.12)] border border-slate-700/50 w-full max-w-2xl mx-auto animate-fade-in-up\">\r\n      <h2 className=\"text-3xl font-bold text-sky-400 mb-6 flex items-center\">\r\n        <Settings className=\"mr-3\" size={30} />\r\n        Settings\r\n      </h2>\r\n\r\n      {error && (\r\n        <div className=\"bg-red-500/20 border border-red-500 text-red-400 p-3 rounded-lg mb-4 flex items-center\">\r\n          <XCircle className=\"mr-2\" size={20} />\r\n          {error}\r\n        </div>\r\n      )}\r\n      {success && (\r\n        <div className=\"bg-green-500/20 border border-green-500 text-green-400 p-3 rounded-lg mb-4 flex items-center\">\r\n          <CheckCircle className=\"mr-2\" size={20} />\r\n          Settings saved successfully!\r\n        </div>\r\n      )}\r\n\r\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n        {/* Notification Settings */}\r\n        <div className=\"bg-slate-700/50 p-4 rounded-lg border border-slate-600\">\r\n          <h3 className=\"text-xl font-semibold text-slate-200 mb-4 flex items-center\">\r\n            <Bell className=\"mr-2\" size={22} /> Notifications\r\n          </h3>\r\n          <div className=\"flex items-center justify-between mb-3\">\r\n            <label htmlFor=\"emailNotifications\" className=\"text-slate-300 flex items-center\">\r\n              <Mail className=\"mr-2\" size={18} /> Email Notifications\r\n            </label>\r\n            <input\r\n              type=\"checkbox\"\r\n              id=\"emailNotifications\"\r\n              name=\"emailNotifications\"\r\n              checked={settings.emailNotifications}\r\n              onChange={handleChange}\r\n              className=\"form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500\"\r\n            />\r\n          </div>\r\n          <div className=\"flex items-center justify-between\">\r\n            <label htmlFor=\"pushNotifications\" className=\"text-slate-300 flex items-center\">\r\n              <Bell className=\"mr-2\" size={18} /> Push Notifications\r\n            </label>\r\n            <input\r\n              type=\"checkbox\"\r\n              id=\"pushNotifications\"\r\n              name=\"pushNotifications\"\r\n              checked={settings.pushNotifications}\r\n              onChange={handleChange}\r\n              className=\"form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Display Settings */}\r\n        <div className=\"bg-slate-700/50 p-4 rounded-lg border border-slate-600\">\r\n          <h3 className=\"text-xl font-semibold text-slate-200 mb-4 flex items-center\">\r\n            <Settings className=\"mr-2\" size={22} /> Display\r\n          </h3>\r\n          <div className=\"flex items-center justify-between\">\r\n            <label htmlFor=\"darkMode\" className=\"text-slate-300 flex items-center\">\r\n              Dark Mode\r\n            </label>\r\n            <input\r\n              type=\"checkbox\"\r\n              id=\"darkMode\"\r\n              name=\"darkMode\"\r\n              checked={settings.darkMode}\r\n              onChange={handleChange}\r\n              className=\"form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Job Search Preferences */}\r\n        <div className=\"bg-slate-700/50 p-4 rounded-lg border border-slate-600\">\r\n          <h3 className=\"text-xl font-semibold text-slate-200 mb-4 flex items-center\">\r\n            <Search className=\"mr-2\" size={22} /> Job Search Preferences\r\n          </h3>\r\n          <div className=\"mb-4\">\r\n            <label htmlFor=\"jobAlertFrequency\" className=\"block text-slate-300 text-sm font-medium mb-2\">\r\n              Job Alert Frequency\r\n            </label>\r\n            <select\r\n              id=\"jobAlertFrequency\"\r\n              name=\"jobAlertFrequency\"\r\n              value={settings.jobAlertFrequency}\r\n              onChange={handleChange}\r\n              className=\"mt-1 block w-full pl-3 pr-10 py-2 text-base bg-slate-900 border border-slate-600 focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm rounded-md text-slate-200\"\r\n            >\r\n              <option value=\"daily\">Daily</option>\r\n              <option value=\"weekly\">Weekly</option>\r\n              <option value=\"monthly\">Monthly</option>\r\n              <option value=\"never\">Never</option>\r\n            </select>\r\n          </div>\r\n          <div>\r\n            <label htmlFor=\"maxDistance\" className=\"block text-slate-300 text-sm font-medium mb-2\">\r\n              Maximum Job Distance (miles)\r\n            </label>\r\n            <input\r\n              type=\"number\"\r\n              id=\"maxDistance\"\r\n              name=\"maxDistance\"\r\n              value={settings.maxDistance}\r\n              onChange={handleChange}\r\n              min=\"1\"\r\n              max=\"500\"\r\n              className=\"mt-1 block w-full pl-3 pr-3 py-2 text-base bg-slate-900 border border-slate-600 focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm rounded-md text-slate-200\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <button\r\n          type=\"submit\"\r\n          className=\"w-full bg-gradient-to-r from-sky-500 to-blue-600 text-white font-bold py-3 px-4 rounded-lg shadow-lg hover:from-sky-600 hover:to-blue-700 transition-all duration-300 ease-in-out flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed\"\r\n          disabled={saving}\r\n        >\r\n          {saving ? (\r\n            <>\r\n              <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n              </svg>\r\n              Saving...\r\n            </>\r\n          ) : (\r\n            <>\r\n              <Save className=\"mr-2\" size={20} />\r\n              Save Settings\r\n            </>\r\n          )}\r\n        </button>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SettingsPage;\r\n"], "mappings": "+IAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,GAAG,CAAEC,MAAM,CAAEC,MAAM,KAAQ,oBAAoB,CACxD,OAASC,QAAQ,CAAEC,IAAI,CAAEC,IAAI,CAAEC,IAAI,CAAEC,OAAO,CAAEC,WAAW,CAAEC,MAAM,KAAQ,cAAc,CACvF,OAASC,cAAc,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEhD,KAAM,CAAAC,YAAY,CAAGC,IAAA,EAA2B,IAA1B,CAAEC,MAAM,CAAEC,EAAE,CAAEC,KAAM,CAAC,CAAAH,IAAA,CACzC,KAAM,CAACI,QAAQ,CAAEC,WAAW,CAAC,CAAGzB,QAAQ,CAAC,CACvC0B,kBAAkB,CAAE,IAAI,CACxBC,iBAAiB,CAAE,KAAK,CACxBC,QAAQ,CAAE,IAAI,CACdC,iBAAiB,CAAE,OAAO,CAC1BC,WAAW,CAAE,EACf,CAAC,CAAC,CACF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGhC,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACiC,MAAM,CAAEC,SAAS,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAACmC,KAAK,CAAEC,QAAQ,CAAC,CAAGpC,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACqC,OAAO,CAAEC,UAAU,CAAC,CAAGtC,QAAQ,CAAC,KAAK,CAAC,CAE7CC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAsC,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CAAClB,MAAM,EAAI,CAACC,EAAE,EAAI,CAACC,KAAK,CAAE,CAC5BS,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CACA,GAAI,CACF,KAAM,CAAAQ,MAAM,CAAGtC,GAAG,CAACoB,EAAE,CAAE,WAAW,CAAEC,KAAK,CAAE,OAAO,CAAEF,MAAM,CAAE,aAAa,CAAE,UAAU,CAAC,CACtF,KAAM,CAAAoB,OAAO,CAAG,KAAM,CAAAtC,MAAM,CAACqC,MAAM,CAAC,CACpC,GAAIC,OAAO,CAACC,MAAM,CAAC,CAAC,CAAE,CACpBjB,WAAW,CAACkB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,EAAKF,OAAO,CAACI,IAAI,CAAC,CAAC,CAAG,CAAC,CACvD,CACF,CAAE,MAAOC,GAAG,CAAE,CACZC,OAAO,CAACZ,KAAK,CAAC,0BAA0B,CAAEW,GAAG,CAAC,CAC9CV,QAAQ,CAAC,4CAA4C,CAAC,CACxD,CAAC,OAAS,CACRJ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDO,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,CAAClB,MAAM,CAAEC,EAAE,CAAEC,KAAK,CAAC,CAAC,CAEvB,KAAM,CAAAyB,YAAY,CAAIC,CAAC,EAAK,CAC1B,KAAM,CAAEC,IAAI,CAAEC,KAAK,CAAEC,IAAI,CAAEC,OAAQ,CAAC,CAAGJ,CAAC,CAACK,MAAM,CAC/C,KAAM,CAAAC,GAAG,CAAGH,IAAI,GAAK,UAAU,CAAGC,OAAO,CAAGD,IAAI,GAAK,QAAQ,CAAGI,QAAQ,CAACL,KAAK,CAAE,EAAE,CAAC,CAAGA,KAAK,CAC3F1B,WAAW,CAACkB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACP,CAACO,IAAI,EAAGK,GAAG,EACX,CAAC,CACL,CAAC,CAED,KAAM,CAAAE,YAAY,CAAG,KAAO,CAAAR,CAAC,EAAK,CAChCA,CAAC,CAACS,cAAc,CAAC,CAAC,CAClBxB,SAAS,CAAC,IAAI,CAAC,CACfE,QAAQ,CAAC,IAAI,CAAC,CACdE,UAAU,CAAC,KAAK,CAAC,CAEjB,GAAI,CAACjB,MAAM,EAAI,CAACC,EAAE,EAAI,CAACC,KAAK,CAAE,CAC5Ba,QAAQ,CAAC,mDAAmD,CAAC,CAC7DF,SAAS,CAAC,KAAK,CAAC,CAChB,OACF,CAEA,GAAI,CACF,KAAM,CAAAM,MAAM,CAAGtC,GAAG,CAACoB,EAAE,CAAE,WAAW,CAAEC,KAAK,CAAE,OAAO,CAAEF,MAAM,CAAE,aAAa,CAAE,UAAU,CAAC,CACtF,KAAM,CAAAjB,MAAM,CAACoC,MAAM,CAAEhB,QAAQ,CAAE,CAAEmC,KAAK,CAAE,IAAK,CAAC,CAAC,CAC/CrB,UAAU,CAAC,IAAI,CAAC,CAChBsB,UAAU,CAAC,IAAMtB,UAAU,CAAC,KAAK,CAAC,CAAE,IAAI,CAAC,CAAE;AAC7C,CAAE,MAAOQ,GAAG,CAAE,CACZC,OAAO,CAACZ,KAAK,CAAC,wBAAwB,CAAEW,GAAG,CAAC,CAC5CV,QAAQ,CAAC,4CAA4C,CAAC,CACxD,CAAC,OAAS,CACRF,SAAS,CAAC,KAAK,CAAC,CAClB,CACF,CAAC,CAED,GAAIH,OAAO,CAAE,CACX,mBACEjB,IAAA,QAAK+C,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpDhD,IAAA,CAACF,cAAc,GAAE,CAAC,CACf,CAAC,CAEV,CAEA,mBACEI,KAAA,QAAK6C,SAAS,CAAC,4JAA4J,CAAAC,QAAA,eACzK9C,KAAA,OAAI6C,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACpEhD,IAAA,CAACT,QAAQ,EAACwD,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,WAEzC,EAAI,CAAC,CAEJ5B,KAAK,eACJnB,KAAA,QAAK6C,SAAS,CAAC,wFAAwF,CAAAC,QAAA,eACrGhD,IAAA,CAACL,OAAO,EAACoD,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,CACrC5B,KAAK,EACH,CACN,CACAE,OAAO,eACNrB,KAAA,QAAK6C,SAAS,CAAC,8FAA8F,CAAAC,QAAA,eAC3GhD,IAAA,CAACJ,WAAW,EAACmD,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,+BAE5C,EAAK,CACN,cAED/C,KAAA,SAAMgD,QAAQ,CAAEP,YAAa,CAACI,SAAS,CAAC,WAAW,CAAAC,QAAA,eAEjD9C,KAAA,QAAK6C,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrE9C,KAAA,OAAI6C,SAAS,CAAC,6DAA6D,CAAAC,QAAA,eACzEhD,IAAA,CAACR,IAAI,EAACuD,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,iBACrC,EAAI,CAAC,cACL/C,KAAA,QAAK6C,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD9C,KAAA,UAAOiD,OAAO,CAAC,oBAAoB,CAACJ,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC9EhD,IAAA,CAACP,IAAI,EAACsD,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,uBACrC,EAAO,CAAC,cACRjD,IAAA,UACEsC,IAAI,CAAC,UAAU,CACfc,EAAE,CAAC,oBAAoB,CACvBhB,IAAI,CAAC,oBAAoB,CACzBG,OAAO,CAAE7B,QAAQ,CAACE,kBAAmB,CACrCyC,QAAQ,CAAEnB,YAAa,CACvBa,SAAS,CAAC,iEAAiE,CAC5E,CAAC,EACC,CAAC,cACN7C,KAAA,QAAK6C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD9C,KAAA,UAAOiD,OAAO,CAAC,mBAAmB,CAACJ,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC7EhD,IAAA,CAACR,IAAI,EAACuD,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,sBACrC,EAAO,CAAC,cACRjD,IAAA,UACEsC,IAAI,CAAC,UAAU,CACfc,EAAE,CAAC,mBAAmB,CACtBhB,IAAI,CAAC,mBAAmB,CACxBG,OAAO,CAAE7B,QAAQ,CAACG,iBAAkB,CACpCwC,QAAQ,CAAEnB,YAAa,CACvBa,SAAS,CAAC,iEAAiE,CAC5E,CAAC,EACC,CAAC,EACH,CAAC,cAGN7C,KAAA,QAAK6C,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrE9C,KAAA,OAAI6C,SAAS,CAAC,6DAA6D,CAAAC,QAAA,eACzEhD,IAAA,CAACT,QAAQ,EAACwD,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,WACzC,EAAI,CAAC,cACL/C,KAAA,QAAK6C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDhD,IAAA,UAAOmD,OAAO,CAAC,UAAU,CAACJ,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,WAEvE,CAAO,CAAC,cACRhD,IAAA,UACEsC,IAAI,CAAC,UAAU,CACfc,EAAE,CAAC,UAAU,CACbhB,IAAI,CAAC,UAAU,CACfG,OAAO,CAAE7B,QAAQ,CAACI,QAAS,CAC3BuC,QAAQ,CAAEnB,YAAa,CACvBa,SAAS,CAAC,iEAAiE,CAC5E,CAAC,EACC,CAAC,EACH,CAAC,cAGN7C,KAAA,QAAK6C,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrE9C,KAAA,OAAI6C,SAAS,CAAC,6DAA6D,CAAAC,QAAA,eACzEhD,IAAA,CAACH,MAAM,EAACkD,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,0BACvC,EAAI,CAAC,cACL/C,KAAA,QAAK6C,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBhD,IAAA,UAAOmD,OAAO,CAAC,mBAAmB,CAACJ,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,qBAE7F,CAAO,CAAC,cACR9C,KAAA,WACEkD,EAAE,CAAC,mBAAmB,CACtBhB,IAAI,CAAC,mBAAmB,CACxBC,KAAK,CAAE3B,QAAQ,CAACK,iBAAkB,CAClCsC,QAAQ,CAAEnB,YAAa,CACvBa,SAAS,CAAC,kLAAkL,CAAAC,QAAA,eAE5LhD,IAAA,WAAQqC,KAAK,CAAC,OAAO,CAAAW,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpChD,IAAA,WAAQqC,KAAK,CAAC,QAAQ,CAAAW,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtChD,IAAA,WAAQqC,KAAK,CAAC,SAAS,CAAAW,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxChD,IAAA,WAAQqC,KAAK,CAAC,OAAO,CAAAW,QAAA,CAAC,OAAK,CAAQ,CAAC,EAC9B,CAAC,EACN,CAAC,cACN9C,KAAA,QAAA8C,QAAA,eACEhD,IAAA,UAAOmD,OAAO,CAAC,aAAa,CAACJ,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,8BAEvF,CAAO,CAAC,cACRhD,IAAA,UACEsC,IAAI,CAAC,QAAQ,CACbc,EAAE,CAAC,aAAa,CAChBhB,IAAI,CAAC,aAAa,CAClBC,KAAK,CAAE3B,QAAQ,CAACM,WAAY,CAC5BqC,QAAQ,CAAEnB,YAAa,CACvBoB,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,KAAK,CACTR,SAAS,CAAC,iLAAiL,CAC5L,CAAC,EACC,CAAC,EACH,CAAC,cAEN/C,IAAA,WACEsC,IAAI,CAAC,QAAQ,CACbS,SAAS,CAAC,oQAAoQ,CAC9QS,QAAQ,CAAErC,MAAO,CAAA6B,QAAA,CAEhB7B,MAAM,cACLjB,KAAA,CAAAE,SAAA,EAAA4C,QAAA,eACE9C,KAAA,QAAK6C,SAAS,CAAC,4CAA4C,CAACU,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAAAX,QAAA,eAC5HhD,IAAA,WAAQ+C,SAAS,CAAC,YAAY,CAACa,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAS,CAAC,cACrGhE,IAAA,SAAM+C,SAAS,CAAC,YAAY,CAACW,IAAI,CAAC,cAAc,CAACO,CAAC,CAAC,iHAAiH,CAAO,CAAC,EACzK,CAAC,YAER,EAAE,CAAC,cAEH/D,KAAA,CAAAE,SAAA,EAAA4C,QAAA,eACEhD,IAAA,CAACN,IAAI,EAACqD,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,gBAErC,EAAE,CACH,CACK,CAAC,EACL,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}