import React from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';

class ComponentErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
    
    // Log error for debugging
    console.error(`ComponentErrorBoundary caught an error in ${this.props.componentName || 'Unknown Component'}:`, error, errorInfo);
    
    // You could also send this to an error reporting service
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState(prevState => ({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      retryCount: prevState.retryCount + 1
    }));
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.handleRetry);
      }

      // Default fallback UI
      return (
        <div className="p-6 bg-red-500/10 border border-red-500/30 rounded-lg text-center">
          <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-400 mb-2">
            {this.props.componentName || 'Component'} Error
          </h3>
          <p className="text-slate-300 mb-4">
            {this.props.errorMessage || 'Something went wrong with this component.'}
          </p>
          
          <button
            onClick={this.handleRetry}
            className="px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded-lg font-medium transition-colors duration-200 flex items-center justify-center mx-auto"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </button>
          
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <details className="mt-4 text-left">
              <summary className="text-red-300 cursor-pointer hover:text-red-200 mb-2 text-sm">
                Show Error Details (Development Mode)
              </summary>
              <div className="bg-slate-800/50 p-3 rounded-lg text-xs font-mono text-slate-400 whitespace-pre-wrap border border-slate-700 max-h-40 overflow-auto">
                {this.state.error && this.state.error.toString()}
                {this.state.errorInfo && this.state.errorInfo.componentStack}
              </div>
            </details>
          )}
          
          {this.state.retryCount > 2 && (
            <p className="text-xs text-slate-500 mt-2">
              If this error persists, please refresh the page or contact support.
            </p>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

export default ComponentErrorBoundary;
