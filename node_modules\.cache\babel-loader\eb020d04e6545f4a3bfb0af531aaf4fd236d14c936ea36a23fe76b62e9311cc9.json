{"ast": null, "code": "/**\n * lucide-react v0.279.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Drama = createLucideIcon(\"Drama\", [[\"path\", {\n  d: \"M 10 11h.01\",\n  key: \"1lksi6\"\n}], [\"path\", {\n  d: \"M 14 6h.01\",\n  key: \"1kxpxo\"\n}], [\"path\", {\n  d: \"M 18 6h.01\",\n  key: \"1goweo\"\n}], [\"path\", {\n  d: \"M 6.5 13.1h.01\",\n  key: \"1i19cj\"\n}], [\"path\", {\n  d: \"M22 5c0 9-4 12-6 12s-6-3-6-12c0-2 2-3 6-3s6 1 6 3\",\n  key: \"172yzv\"\n}], [\"path\", {\n  d: \"M17.4 9.9c-.8.8-2 .8-2.8 0\",\n  key: \"1obv0w\"\n}], [\"path\", {\n  d: \"M10.1 7.1C9 7.2 7.7 7.7 6 8.6c-3.5 2-4.7 3.9-3.7 5.6 4.5 7.8 9.5 8.4 11.2 7.4.9-.5 1.9-2.1 1.9-4.7\",\n  key: \"rqjl8i\"\n}], [\"path\", {\n  d: \"M9.1 16.5c.3-1.1 1.4-1.7 2.4-1.4\",\n  key: \"1mr6wy\"\n}]]);\nexport { Drama as default };", "map": {"version": 3, "names": ["Drama", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Documents\\Repo\\Auto_Job_Application\\node_modules\\lucide-react\\src\\icons\\drama.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Drama\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNIDEwIDExaC4wMSIvPgogIDxwYXRoIGQ9Ik0gMTQgNmguMDEiLz4KICA8cGF0aCBkPSJNIDE4IDZoLjAxIi8+CiAgPHBhdGggZD0iTSA2LjUgMTMuMWguMDEiLz4KICA8cGF0aCBkPSJNMjIgNWMwIDktNCAxMi02IDEycy02LTMtNi0xMmMwLTIgMi0zIDYtM3M2IDEgNiAzIi8+CiAgPHBhdGggZD0iTTE3LjQgOS45Yy0uOC44LTIgLjgtMi44IDAiLz4KICA8cGF0aCBkPSJNMTAuMSA3LjFDOSA3LjIgNy43IDcuNyA2IDguNmMtMy41IDItNC43IDMuOS0zLjcgNS42IDQuNSA3LjggOS41IDguNCAxMS4yIDcuNC45LS41IDEuOS0yLjEgMS45LTQuNyIvPgogIDxwYXRoIGQ9Ik05LjEgMTYuNWMuMy0xLjEgMS40LTEuNyAyLjQtMS40Ii8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/drama\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Drama = createLucideIcon('Drama', [\n  ['path', { d: 'M 10 11h.01', key: '1lksi6' }],\n  ['path', { d: 'M 14 6h.01', key: '1kxpxo' }],\n  ['path', { d: 'M 18 6h.01', key: '1goweo' }],\n  ['path', { d: 'M 6.5 13.1h.01', key: '1i19cj' }],\n  [\n    'path',\n    { d: 'M22 5c0 9-4 12-6 12s-6-3-6-12c0-2 2-3 6-3s6 1 6 3', key: '172yzv' },\n  ],\n  ['path', { d: 'M17.4 9.9c-.8.8-2 .8-2.8 0', key: '1obv0w' }],\n  [\n    'path',\n    {\n      d: 'M10.1 7.1C9 7.2 7.7 7.7 6 8.6c-3.5 2-4.7 3.9-3.7 5.6 4.5 7.8 9.5 8.4 11.2 7.4.9-.5 1.9-2.1 1.9-4.7',\n      key: 'rqjl8i',\n    },\n  ],\n  ['path', { d: 'M9.1 16.5c.3-1.1 1.4-1.7 2.4-1.4', key: '1mr6wy' }],\n]);\n\nexport default Drama;\n"], "mappings": ";;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,MAAQ;EAAEC,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CACE,QACA;EAAED,CAAA,EAAG,mDAAqD;EAAAC,GAAA,EAAK;AAAS,EAC1E,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,kCAAoC;EAAAC,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}