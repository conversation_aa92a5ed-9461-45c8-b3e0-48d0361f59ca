{"ast": null, "code": "import{googleAIApiKey}from'../firebaseConfig';import axios from'axios';// This service handles interactions with the Google Gemini API for AI-powered features\nexport const generateProfileSummary=async(skills,experiences)=>{// Create a well-formatted prompt for the AI\nconst prompt=\"Based on the following key skills and experiences, generate a concise and professional profile summary (around 2-3 sentences):\\n\\n\\nSkills: \".concat(skills,\"\\n\\n\\nExperiences:\\n\").concat(experiences.map(exp=>\"- \".concat(exp.title,\" at \").concat(exp.company,\": \").concat(exp.summary)).join('\\n'));// Check if API key is set to a placeholder or empty\nif(!googleAIApiKey||googleAIApiKey==='YOUR_GEMINI_API_KEY'){console.warn('Missing Gemini API key. Using fallback mechanism.');return provideFallbackSummary(skills,experiences);}try{var _response$data,_response$data$candid,_response$data$candid2,_response$data$candid3,_response$data$candid4;// Create the request payload for Gemini API\nconst chatHistory=[{role:\"user\",parts:[{text:prompt}]}];const payload={contents:chatHistory};// Make the API request to Gemini\nconst response=await axios.post(\"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=\".concat(googleAIApiKey),payload,{headers:{'Content-Type':'application/json'},timeout:10000// Add timeout to prevent hanging requests\n});// Extract and return the generated text\nif(((_response$data=response.data)===null||_response$data===void 0?void 0:(_response$data$candid=_response$data.candidates)===null||_response$data$candid===void 0?void 0:_response$data$candid.length)>0&&((_response$data$candid2=response.data.candidates[0])===null||_response$data$candid2===void 0?void 0:(_response$data$candid3=_response$data$candid2.content)===null||_response$data$candid3===void 0?void 0:(_response$data$candid4=_response$data$candid3.parts)===null||_response$data$candid4===void 0?void 0:_response$data$candid4.length)>0){return{success:true,summary:response.data.candidates[0].content.parts[0].text};}else{console.warn(\"Unexpected API response structure, using fallback\");return provideFallbackSummary(skills,experiences);}}catch(error){console.error(\"Error generating profile summary:\",error);// Use fallback on error\nreturn provideFallbackSummary(skills,experiences,error.message);}};// Provide a fallback summary when the API is unavailable\nfunction provideFallbackSummary(skills,experiences){let errorMessage=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;// Extract key information\nconst skillsList=skills.split(',').map(skill=>skill.trim()).filter(s=>s);const topSkills=skillsList.slice(0,3).join(', ');// Find most recent experience with non-empty title and company\nconst validExperiences=experiences.filter(exp=>exp.title&&exp.company);const recentExp=validExperiences.length>0?validExperiences[0]:null;let summary='';if(errorMessage){summary=\"[Note: AI generation failed (\".concat(errorMessage,\"). Using template summary instead.] \");}else{summary='[Note: Using template summary as AI is unavailable. Add your API key for AI-generated summaries.] ';}if(topSkills&&recentExp){summary+=\"Experienced professional with expertise in \".concat(topSkills)+\" and a background as \".concat(recentExp.title,\" at \").concat(recentExp.company,\".\")+\" Skilled in \".concat(skillsList.length>3?'various areas including '+topSkills+' and more':topSkills,\",\")+\" with a proven track record of delivering results in \".concat(validExperiences.length>1?'multiple roles':'the field',\".\");}else if(topSkills){summary+=\"Professional with expertise in \".concat(topSkills)+\" and a passion for delivering high-quality results.\"+\" Looking to leverage skills in \".concat(skillsList.length>3?'various areas including '+topSkills+' and more':topSkills)+\" to contribute to organizational success.\";}else if(recentExp){summary+=\"Experienced \".concat(recentExp.title,\" with a background at \").concat(recentExp.company,\",\")+\" focusing on delivering exceptional results and driving success through dedicated efforts.\";}else{summary+=\"Detail-oriented professional with a strong work ethic and commitment to excellence.\"+\" Seeking to leverage diverse skills and experience to contribute to organizational goals.\";}return{success:true,summary:summary,isAiFallback:true};}// Additional AI feature: Generate job application letter based on profile and job description\nexport const generateCoverLetter=async(profile,jobDescription)=>{const prompt=\"Generate a professional, personalized cover letter for a job application based on the following:\\n  \\nJob Description: \".concat(jobDescription,\"\\n\\nAbout the applicant:\\n- Name: \").concat(profile.name,\"\\n- Skills: \").concat(profile.keySkills,\"\\n- Experience: \").concat(profile.experiences.map(exp=>\"\".concat(exp.title,\" at \").concat(exp.company,\" (\").concat(exp.years,\") - \").concat(exp.summary)).join('\\n'),\"\\n\\nThe cover letter should be concise, professional, highlight relevant skills and experiences, and express genuine interest in the position.\");// Check if API key is set to a placeholder or empty\nif(!googleAIApiKey||googleAIApiKey==='YOUR_GEMINI_API_KEY'){console.warn('Missing Gemini API key. Using fallback cover letter mechanism.');return provideFallbackCoverLetter(profile,jobDescription);}try{var _response$data2,_response$data2$candi,_response$data$candid5,_response$data$candid6,_response$data$candid7;const chatHistory=[{role:\"user\",parts:[{text:prompt}]}];const payload={contents:chatHistory};const response=await axios.post(\"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=\".concat(googleAIApiKey),payload,{headers:{'Content-Type':'application/json'},timeout:15000// Add timeout to prevent hanging requests\n});if(((_response$data2=response.data)===null||_response$data2===void 0?void 0:(_response$data2$candi=_response$data2.candidates)===null||_response$data2$candi===void 0?void 0:_response$data2$candi.length)>0&&((_response$data$candid5=response.data.candidates[0])===null||_response$data$candid5===void 0?void 0:(_response$data$candid6=_response$data$candid5.content)===null||_response$data$candid6===void 0?void 0:(_response$data$candid7=_response$data$candid6.parts)===null||_response$data$candid7===void 0?void 0:_response$data$candid7.length)>0){return{success:true,coverLetter:response.data.candidates[0].content.parts[0].text};}else{console.warn(\"Unexpected API response structure, using fallback cover letter\");return provideFallbackCoverLetter(profile,jobDescription);}}catch(error){console.error(\"Error generating cover letter:\",error);// Use fallback on error\nreturn provideFallbackCoverLetter(profile,jobDescription,error.message);}};// Provide a fallback cover letter when the API is unavailable\nfunction provideFallbackCoverLetter(profile,jobDescription){let errorMessage=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;const{name,keySkills,experiences}=profile;// Extract key job information from description\nconst jobTitleMatch=jobDescription.match(/(?:position|job|role)\\s*(?:of|as|:)?\\s*([^.,\\n]+)/i);const jobTitle=jobTitleMatch?jobTitleMatch[1].trim():\"the open position\";// Extract company name if present\nconst companyMatch=jobDescription.match(/(?:at|with|for)\\s+([A-Z][a-zA-Z\\s]+)(?:[.,]|\\s+is|\\s+are|\\s+has|\\s+have)/i);const company=companyMatch?companyMatch[1].trim():\"your company\";// Extract skills from profile\nconst skills=keySkills.split(',').map(s=>s.trim()).filter(s=>s);const skillsText=skills.length>0?\"\".concat(skills.slice(0,3).join(', ')).concat(skills.length>3?', and other relevant skills':''):'my professional skills';// Get most recent experience\nconst validExperiences=experiences.filter(exp=>exp.title&&exp.company);const recentExp=validExperiences.length>0?validExperiences[0]:null;// Create fallback letter\nlet warning='';if(errorMessage){warning=\"[Note: AI generation failed (\".concat(errorMessage,\"). Using template cover letter instead.]\\n\\n\");}else{warning='[Note: Using template cover letter as AI is unavailable. Add your API key for AI-generated content.]\\n\\n';}const date=new Date().toLocaleDateString('en-US',{year:'numeric',month:'long',day:'numeric'});const letter=\"\".concat(warning).concat(date,\"\\n\\nDear Hiring Manager,\\n\\n\")+\"I am writing to express my interest in \".concat(jobTitle,\" at \").concat(company,\". With my background \").concat(recentExp?\"as \".concat(recentExp.title,\" at \").concat(recentExp.company):'in the field')+\" and expertise in \".concat(skillsText,\", I believe I would be a valuable addition to your team.\\n\\n\")+\"Based on the job description, my qualifications align well with your requirements. \".concat(recentExp&&recentExp.summary?\"During my time at \".concat(recentExp.company,\", \").concat(recentExp.summary):'')+\" I am confident that these experiences have prepared me to excel in this role.\\n\\n\"+\"I am particularly interested in this position because it allows me to leverage my strengths in \".concat(skillsText,\" while contributing to \").concat(company,\"'s goals and objectives.\")+\" I am excited about the opportunity to bring my skills and enthusiasm to your team.\\n\\n\"+\"Thank you for considering my application. I look forward to the possibility of discussing how I can contribute to \".concat(company,\".\\n\\n\")+\"Sincerely,\\n\\n\".concat(name||'Your Name');return{success:true,coverLetter:letter,isAiFallback:true};}", "map": {"version": 3, "names": ["googleAIApiKey", "axios", "generateProfileSummary", "skills", "experiences", "prompt", "concat", "map", "exp", "title", "company", "summary", "join", "console", "warn", "provideFallbackSummary", "_response$data", "_response$data$candid", "_response$data$candid2", "_response$data$candid3", "_response$data$candid4", "chatHistory", "role", "parts", "text", "payload", "contents", "response", "post", "headers", "timeout", "data", "candidates", "length", "content", "success", "error", "message", "errorMessage", "arguments", "undefined", "skillsList", "split", "skill", "trim", "filter", "s", "topSkills", "slice", "validExperiences", "recentExp", "isAiFallback", "generateCoverLetter", "profile", "jobDescription", "name", "keySkills", "years", "provideFallbackCoverLetter", "_response$data2", "_response$data2$candi", "_response$data$candid5", "_response$data$candid6", "_response$data$candid7", "coverLetter", "jobTitleMatch", "match", "jobTitle", "companyMatch", "skillsText", "warning", "date", "Date", "toLocaleDateString", "year", "month", "day", "letter"], "sources": ["C:/Users/<USER>/Documents/Repo/Auto_Job_Application/src/services/aiService.js"], "sourcesContent": ["import { googleAIApi<PERSON>ey } from '../firebaseConfig';\nimport axios from 'axios';\n\n// This service handles interactions with the Google Gemini API for AI-powered features\n\nexport const generateProfileSummary = async (skills, experiences) => {\n  // Create a well-formatted prompt for the AI\n  const prompt = `Based on the following key skills and experiences, generate a concise and professional profile summary (around 2-3 sentences):\\n\\n\nSkills: ${skills}\\n\\n\nExperiences:\\n${experiences.map(exp => `- ${exp.title} at ${exp.company}: ${exp.summary}`).join('\\n')}`;\n  \n  // Check if API key is set to a placeholder or empty\n  if (!googleAIApiKey || googleAIApiKey === 'YOUR_GEMINI_API_KEY') {\n    console.warn('Missing Gemini API key. Using fallback mechanism.');\n    return provideFallbackSummary(skills, experiences);\n  }\n  \n  try {\n    // Create the request payload for Gemini API\n    const chatHistory = [{ role: \"user\", parts: [{ text: prompt }] }];\n    const payload = { contents: chatHistory };\n    \n    // Make the API request to Gemini\n    const response = await axios.post(\n      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${googleAIApiKey}`,\n      payload,\n      {\n        headers: { 'Content-Type': 'application/json' },\n        timeout: 10000 // Add timeout to prevent hanging requests\n      }\n    );\n    \n    // Extract and return the generated text\n    if (response.data?.candidates?.length > 0 && \n        response.data.candidates[0]?.content?.parts?.length > 0) {\n      return {\n        success: true,\n        summary: response.data.candidates[0].content.parts[0].text\n      };\n    } else {\n      console.warn(\"Unexpected API response structure, using fallback\");\n      return provideFallbackSummary(skills, experiences);\n    }\n  } catch (error) {\n    console.error(\"Error generating profile summary:\", error);\n    // Use fallback on error\n    return provideFallbackSummary(skills, experiences, error.message);\n  }\n};\n\n// Provide a fallback summary when the API is unavailable\nfunction provideFallbackSummary(skills, experiences, errorMessage = null) {\n  // Extract key information\n  const skillsList = skills.split(',').map(skill => skill.trim()).filter(s => s);\n  const topSkills = skillsList.slice(0, 3).join(', ');\n  \n  // Find most recent experience with non-empty title and company\n  const validExperiences = experiences.filter(exp => exp.title && exp.company);\n  const recentExp = validExperiences.length > 0 ? validExperiences[0] : null;\n  \n  let summary = '';\n  \n  if (errorMessage) {\n    summary = `[Note: AI generation failed (${errorMessage}). Using template summary instead.] `;\n  } else {\n    summary = '[Note: Using template summary as AI is unavailable. Add your API key for AI-generated summaries.] ';\n  }\n  \n  if (topSkills && recentExp) {\n    summary += `Experienced professional with expertise in ${topSkills}` +\n              ` and a background as ${recentExp.title} at ${recentExp.company}.` +\n              ` Skilled in ${skillsList.length > 3 ? 'various areas including ' + topSkills + ' and more' : topSkills},` +\n              ` with a proven track record of delivering results in ${validExperiences.length > 1 ? 'multiple roles' : 'the field'}.`;\n  } else if (topSkills) {\n    summary += `Professional with expertise in ${topSkills}` +\n              ` and a passion for delivering high-quality results.` +\n              ` Looking to leverage skills in ${skillsList.length > 3 ? 'various areas including ' + topSkills + ' and more' : topSkills}` +\n              ` to contribute to organizational success.`;\n  } else if (recentExp) {\n    summary += `Experienced ${recentExp.title} with a background at ${recentExp.company},` +\n              ` focusing on delivering exceptional results and driving success through dedicated efforts.`;\n  } else {\n    summary += `Detail-oriented professional with a strong work ethic and commitment to excellence.` +\n              ` Seeking to leverage diverse skills and experience to contribute to organizational goals.`;\n  }\n  \n  return {\n    success: true,\n    summary: summary,\n    isAiFallback: true\n  };\n}\n\n// Additional AI feature: Generate job application letter based on profile and job description\nexport const generateCoverLetter = async (profile, jobDescription) => {\n  const prompt = `Generate a professional, personalized cover letter for a job application based on the following:\n  \nJob Description: ${jobDescription}\n\nAbout the applicant:\n- Name: ${profile.name}\n- Skills: ${profile.keySkills}\n- Experience: ${profile.experiences.map(exp => `${exp.title} at ${exp.company} (${exp.years}) - ${exp.summary}`).join('\\n')}\n\nThe cover letter should be concise, professional, highlight relevant skills and experiences, and express genuine interest in the position.`;\n\n  // Check if API key is set to a placeholder or empty\n  if (!googleAIApiKey || googleAIApiKey === 'YOUR_GEMINI_API_KEY') {\n    console.warn('Missing Gemini API key. Using fallback cover letter mechanism.');\n    return provideFallbackCoverLetter(profile, jobDescription);\n  }\n\n  try {\n    const chatHistory = [{ role: \"user\", parts: [{ text: prompt }] }];\n    const payload = { contents: chatHistory };\n    \n    const response = await axios.post(\n      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${googleAIApiKey}`,\n      payload,\n      {\n        headers: { 'Content-Type': 'application/json' },\n        timeout: 15000 // Add timeout to prevent hanging requests\n      }\n    );\n    \n    if (response.data?.candidates?.length > 0 && \n        response.data.candidates[0]?.content?.parts?.length > 0) {\n      return {\n        success: true,\n        coverLetter: response.data.candidates[0].content.parts[0].text\n      };\n    } else {\n      console.warn(\"Unexpected API response structure, using fallback cover letter\");\n      return provideFallbackCoverLetter(profile, jobDescription);\n    }\n  } catch (error) {\n    console.error(\"Error generating cover letter:\", error);\n    // Use fallback on error\n    return provideFallbackCoverLetter(profile, jobDescription, error.message);\n  }\n};\n\n// Provide a fallback cover letter when the API is unavailable\nfunction provideFallbackCoverLetter(profile, jobDescription, errorMessage = null) {\n  const { name, keySkills, experiences } = profile;\n  \n  // Extract key job information from description\n  const jobTitleMatch = jobDescription.match(/(?:position|job|role)\\s*(?:of|as|:)?\\s*([^.,\\n]+)/i);\n  const jobTitle = jobTitleMatch ? jobTitleMatch[1].trim() : \"the open position\";\n  \n  // Extract company name if present\n  const companyMatch = jobDescription.match(/(?:at|with|for)\\s+([A-Z][a-zA-Z\\s]+)(?:[.,]|\\s+is|\\s+are|\\s+has|\\s+have)/i);\n  const company = companyMatch ? companyMatch[1].trim() : \"your company\";\n  \n  // Extract skills from profile\n  const skills = keySkills.split(',').map(s => s.trim()).filter(s => s);\n  const skillsText = skills.length > 0 ? \n    `${skills.slice(0, 3).join(', ')}${skills.length > 3 ? ', and other relevant skills' : ''}` : \n    'my professional skills';\n  \n  // Get most recent experience\n  const validExperiences = experiences.filter(exp => exp.title && exp.company);\n  const recentExp = validExperiences.length > 0 ? validExperiences[0] : null;\n  \n  // Create fallback letter\n  let warning = '';\n  if (errorMessage) {\n    warning = `[Note: AI generation failed (${errorMessage}). Using template cover letter instead.]\\n\\n`;\n  } else {\n    warning = '[Note: Using template cover letter as AI is unavailable. Add your API key for AI-generated content.]\\n\\n';\n  }\n  \n  const date = new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\n  \n  const letter = `${warning}${date}\\n\\nDear Hiring Manager,\\n\\n`+\n    `I am writing to express my interest in ${jobTitle} at ${company}. With my background ${recentExp ? `as ${recentExp.title} at ${recentExp.company}` : 'in the field'}`+\n    ` and expertise in ${skillsText}, I believe I would be a valuable addition to your team.\\n\\n`+\n    `Based on the job description, my qualifications align well with your requirements. ${recentExp && recentExp.summary ? `During my time at ${recentExp.company}, ${recentExp.summary}` : ''}`+\n    ` I am confident that these experiences have prepared me to excel in this role.\\n\\n`+\n    `I am particularly interested in this position because it allows me to leverage my strengths in ${skillsText} while contributing to ${company}'s goals and objectives.`+\n    ` I am excited about the opportunity to bring my skills and enthusiasm to your team.\\n\\n`+\n    `Thank you for considering my application. I look forward to the possibility of discussing how I can contribute to ${company}.\\n\\n`+\n    `Sincerely,\\n\\n${name || 'Your Name'}`;\n  \n  return {\n    success: true,\n    coverLetter: letter,\n    isAiFallback: true\n  };\n}\n"], "mappings": "AAAA,OAASA,cAAc,KAAQ,mBAAmB,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CAEzB;AAEA,MAAO,MAAM,CAAAC,sBAAsB,CAAG,KAAAA,CAAOC,MAAM,CAAEC,WAAW,GAAK,CACnE;AACA,KAAM,CAAAC,MAAM,gJAAAC,MAAA,CACJH,MAAM,yBAAAG,MAAA,CACAF,WAAW,CAACG,GAAG,CAACC,GAAG,OAAAF,MAAA,CAASE,GAAG,CAACC,KAAK,SAAAH,MAAA,CAAOE,GAAG,CAACE,OAAO,OAAAJ,MAAA,CAAKE,GAAG,CAACG,OAAO,CAAE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAE,CAErG;AACA,GAAI,CAACZ,cAAc,EAAIA,cAAc,GAAK,qBAAqB,CAAE,CAC/Da,OAAO,CAACC,IAAI,CAAC,mDAAmD,CAAC,CACjE,MAAO,CAAAC,sBAAsB,CAACZ,MAAM,CAAEC,WAAW,CAAC,CACpD,CAEA,GAAI,KAAAY,cAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACF;AACA,KAAM,CAAAC,WAAW,CAAG,CAAC,CAAEC,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,CAAC,CAAEC,IAAI,CAAEnB,MAAO,CAAC,CAAE,CAAC,CAAC,CACjE,KAAM,CAAAoB,OAAO,CAAG,CAAEC,QAAQ,CAAEL,WAAY,CAAC,CAEzC;AACA,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAA1B,KAAK,CAAC2B,IAAI,iGAAAtB,MAAA,CACiEN,cAAc,EAC9GyB,OAAO,CACP,CACEI,OAAO,CAAE,CAAE,cAAc,CAAE,kBAAmB,CAAC,CAC/CC,OAAO,CAAE,KAAM;AACjB,CACF,CAAC,CAED;AACA,GAAI,EAAAd,cAAA,CAAAW,QAAQ,CAACI,IAAI,UAAAf,cAAA,kBAAAC,qBAAA,CAAbD,cAAA,CAAegB,UAAU,UAAAf,qBAAA,iBAAzBA,qBAAA,CAA2BgB,MAAM,EAAG,CAAC,EACrC,EAAAf,sBAAA,CAAAS,QAAQ,CAACI,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,UAAAd,sBAAA,kBAAAC,sBAAA,CAA3BD,sBAAA,CAA6BgB,OAAO,UAAAf,sBAAA,kBAAAC,sBAAA,CAApCD,sBAAA,CAAsCI,KAAK,UAAAH,sBAAA,iBAA3CA,sBAAA,CAA6Ca,MAAM,EAAG,CAAC,CAAE,CAC3D,MAAO,CACLE,OAAO,CAAE,IAAI,CACbxB,OAAO,CAAEgB,QAAQ,CAACI,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,CAACE,OAAO,CAACX,KAAK,CAAC,CAAC,CAAC,CAACC,IACxD,CAAC,CACH,CAAC,IAAM,CACLX,OAAO,CAACC,IAAI,CAAC,mDAAmD,CAAC,CACjE,MAAO,CAAAC,sBAAsB,CAACZ,MAAM,CAAEC,WAAW,CAAC,CACpD,CACF,CAAE,MAAOgC,KAAK,CAAE,CACdvB,OAAO,CAACuB,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzD;AACA,MAAO,CAAArB,sBAAsB,CAACZ,MAAM,CAAEC,WAAW,CAAEgC,KAAK,CAACC,OAAO,CAAC,CACnE,CACF,CAAC,CAED;AACA,QAAS,CAAAtB,sBAAsBA,CAACZ,MAAM,CAAEC,WAAW,CAAuB,IAArB,CAAAkC,YAAY,CAAAC,SAAA,CAAAN,MAAA,IAAAM,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,IAAI,CACtE;AACA,KAAM,CAAAE,UAAU,CAAGtC,MAAM,CAACuC,KAAK,CAAC,GAAG,CAAC,CAACnC,GAAG,CAACoC,KAAK,EAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC,CAC9E,KAAM,CAAAC,SAAS,CAAGN,UAAU,CAACO,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACpC,IAAI,CAAC,IAAI,CAAC,CAEnD;AACA,KAAM,CAAAqC,gBAAgB,CAAG7C,WAAW,CAACyC,MAAM,CAACrC,GAAG,EAAIA,GAAG,CAACC,KAAK,EAAID,GAAG,CAACE,OAAO,CAAC,CAC5E,KAAM,CAAAwC,SAAS,CAAGD,gBAAgB,CAAChB,MAAM,CAAG,CAAC,CAAGgB,gBAAgB,CAAC,CAAC,CAAC,CAAG,IAAI,CAE1E,GAAI,CAAAtC,OAAO,CAAG,EAAE,CAEhB,GAAI2B,YAAY,CAAE,CAChB3B,OAAO,iCAAAL,MAAA,CAAmCgC,YAAY,wCAAsC,CAC9F,CAAC,IAAM,CACL3B,OAAO,CAAG,oGAAoG,CAChH,CAEA,GAAIoC,SAAS,EAAIG,SAAS,CAAE,CAC1BvC,OAAO,EAAI,8CAAAL,MAAA,CAA8CyC,SAAS,0BAAAzC,MAAA,CAChC4C,SAAS,CAACzC,KAAK,SAAAH,MAAA,CAAO4C,SAAS,CAACxC,OAAO,KAAG,gBAAAJ,MAAA,CACnDmC,UAAU,CAACR,MAAM,CAAG,CAAC,CAAG,0BAA0B,CAAGc,SAAS,CAAG,WAAW,CAAGA,SAAS,KAAG,yDAAAzC,MAAA,CAClD2C,gBAAgB,CAAChB,MAAM,CAAG,CAAC,CAAG,gBAAgB,CAAG,WAAW,KAAG,CACnI,CAAC,IAAM,IAAIc,SAAS,CAAE,CACpBpC,OAAO,EAAI,kCAAAL,MAAA,CAAkCyC,SAAS,uDACS,mCAAAzC,MAAA,CACnBmC,UAAU,CAACR,MAAM,CAAG,CAAC,CAAG,0BAA0B,CAAGc,SAAS,CAAG,WAAW,CAAGA,SAAS,CAAE,4CACjF,CACvD,CAAC,IAAM,IAAIG,SAAS,CAAE,CACpBvC,OAAO,EAAI,eAAAL,MAAA,CAAe4C,SAAS,CAACzC,KAAK,2BAAAH,MAAA,CAAyB4C,SAAS,CAACxC,OAAO,kGACmB,CACxG,CAAC,IAAM,CACLC,OAAO,EAAI,iLAC0F,CACvG,CAEA,MAAO,CACLwB,OAAO,CAAE,IAAI,CACbxB,OAAO,CAAEA,OAAO,CAChBwC,YAAY,CAAE,IAChB,CAAC,CACH,CAEA;AACA,MAAO,MAAM,CAAAC,mBAAmB,CAAG,KAAAA,CAAOC,OAAO,CAAEC,cAAc,GAAK,CACpE,KAAM,CAAAjD,MAAM,2HAAAC,MAAA,CAEKgD,cAAc,uCAAAhD,MAAA,CAGvB+C,OAAO,CAACE,IAAI,iBAAAjD,MAAA,CACV+C,OAAO,CAACG,SAAS,qBAAAlD,MAAA,CACb+C,OAAO,CAACjD,WAAW,CAACG,GAAG,CAACC,GAAG,KAAAF,MAAA,CAAOE,GAAG,CAACC,KAAK,SAAAH,MAAA,CAAOE,GAAG,CAACE,OAAO,OAAAJ,MAAA,CAAKE,GAAG,CAACiD,KAAK,SAAAnD,MAAA,CAAOE,GAAG,CAACG,OAAO,CAAE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,kJAEgB,CAEzI;AACA,GAAI,CAACZ,cAAc,EAAIA,cAAc,GAAK,qBAAqB,CAAE,CAC/Da,OAAO,CAACC,IAAI,CAAC,gEAAgE,CAAC,CAC9E,MAAO,CAAA4C,0BAA0B,CAACL,OAAO,CAAEC,cAAc,CAAC,CAC5D,CAEA,GAAI,KAAAK,eAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACF,KAAM,CAAA1C,WAAW,CAAG,CAAC,CAAEC,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,CAAC,CAAEC,IAAI,CAAEnB,MAAO,CAAC,CAAE,CAAC,CAAC,CACjE,KAAM,CAAAoB,OAAO,CAAG,CAAEC,QAAQ,CAAEL,WAAY,CAAC,CAEzC,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAA1B,KAAK,CAAC2B,IAAI,iGAAAtB,MAAA,CACiEN,cAAc,EAC9GyB,OAAO,CACP,CACEI,OAAO,CAAE,CAAE,cAAc,CAAE,kBAAmB,CAAC,CAC/CC,OAAO,CAAE,KAAM;AACjB,CACF,CAAC,CAED,GAAI,EAAA6B,eAAA,CAAAhC,QAAQ,CAACI,IAAI,UAAA4B,eAAA,kBAAAC,qBAAA,CAAbD,eAAA,CAAe3B,UAAU,UAAA4B,qBAAA,iBAAzBA,qBAAA,CAA2B3B,MAAM,EAAG,CAAC,EACrC,EAAA4B,sBAAA,CAAAlC,QAAQ,CAACI,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,UAAA6B,sBAAA,kBAAAC,sBAAA,CAA3BD,sBAAA,CAA6B3B,OAAO,UAAA4B,sBAAA,kBAAAC,sBAAA,CAApCD,sBAAA,CAAsCvC,KAAK,UAAAwC,sBAAA,iBAA3CA,sBAAA,CAA6C9B,MAAM,EAAG,CAAC,CAAE,CAC3D,MAAO,CACLE,OAAO,CAAE,IAAI,CACb6B,WAAW,CAAErC,QAAQ,CAACI,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,CAACE,OAAO,CAACX,KAAK,CAAC,CAAC,CAAC,CAACC,IAC5D,CAAC,CACH,CAAC,IAAM,CACLX,OAAO,CAACC,IAAI,CAAC,gEAAgE,CAAC,CAC9E,MAAO,CAAA4C,0BAA0B,CAACL,OAAO,CAAEC,cAAc,CAAC,CAC5D,CACF,CAAE,MAAOlB,KAAK,CAAE,CACdvB,OAAO,CAACuB,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD;AACA,MAAO,CAAAsB,0BAA0B,CAACL,OAAO,CAAEC,cAAc,CAAElB,KAAK,CAACC,OAAO,CAAC,CAC3E,CACF,CAAC,CAED;AACA,QAAS,CAAAqB,0BAA0BA,CAACL,OAAO,CAAEC,cAAc,CAAuB,IAArB,CAAAhB,YAAY,CAAAC,SAAA,CAAAN,MAAA,IAAAM,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,IAAI,CAC9E,KAAM,CAAEgB,IAAI,CAAEC,SAAS,CAAEpD,WAAY,CAAC,CAAGiD,OAAO,CAEhD;AACA,KAAM,CAAAY,aAAa,CAAGX,cAAc,CAACY,KAAK,CAAC,oDAAoD,CAAC,CAChG,KAAM,CAAAC,QAAQ,CAAGF,aAAa,CAAGA,aAAa,CAAC,CAAC,CAAC,CAACrB,IAAI,CAAC,CAAC,CAAG,mBAAmB,CAE9E;AACA,KAAM,CAAAwB,YAAY,CAAGd,cAAc,CAACY,KAAK,CAAC,2EAA2E,CAAC,CACtH,KAAM,CAAAxD,OAAO,CAAG0D,YAAY,CAAGA,YAAY,CAAC,CAAC,CAAC,CAACxB,IAAI,CAAC,CAAC,CAAG,cAAc,CAEtE;AACA,KAAM,CAAAzC,MAAM,CAAGqD,SAAS,CAACd,KAAK,CAAC,GAAG,CAAC,CAACnC,GAAG,CAACuC,CAAC,EAAIA,CAAC,CAACF,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC,CACrE,KAAM,CAAAuB,UAAU,CAAGlE,MAAM,CAAC8B,MAAM,CAAG,CAAC,IAAA3B,MAAA,CAC/BH,MAAM,CAAC6C,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACpC,IAAI,CAAC,IAAI,CAAC,EAAAN,MAAA,CAAGH,MAAM,CAAC8B,MAAM,CAAG,CAAC,CAAG,6BAA6B,CAAG,EAAE,EACzF,wBAAwB,CAE1B;AACA,KAAM,CAAAgB,gBAAgB,CAAG7C,WAAW,CAACyC,MAAM,CAACrC,GAAG,EAAIA,GAAG,CAACC,KAAK,EAAID,GAAG,CAACE,OAAO,CAAC,CAC5E,KAAM,CAAAwC,SAAS,CAAGD,gBAAgB,CAAChB,MAAM,CAAG,CAAC,CAAGgB,gBAAgB,CAAC,CAAC,CAAC,CAAG,IAAI,CAE1E;AACA,GAAI,CAAAqB,OAAO,CAAG,EAAE,CAChB,GAAIhC,YAAY,CAAE,CAChBgC,OAAO,iCAAAhE,MAAA,CAAmCgC,YAAY,gDAA8C,CACtG,CAAC,IAAM,CACLgC,OAAO,CAAG,0GAA0G,CACtH,CAEA,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAE,CAAEC,IAAI,CAAE,SAAS,CAAEC,KAAK,CAAE,MAAM,CAAEC,GAAG,CAAE,SAAU,CAAC,CAAC,CAEvG,KAAM,CAAAC,MAAM,CAAG,GAAAvE,MAAA,CAAGgE,OAAO,EAAAhE,MAAA,CAAGiE,IAAI,2EAAAjE,MAAA,CACY6D,QAAQ,SAAA7D,MAAA,CAAOI,OAAO,0BAAAJ,MAAA,CAAwB4C,SAAS,OAAA5C,MAAA,CAAS4C,SAAS,CAACzC,KAAK,SAAAH,MAAA,CAAO4C,SAAS,CAACxC,OAAO,EAAK,cAAc,CAAE,sBAAAJ,MAAA,CACjJ+D,UAAU,gEAA8D,uFAAA/D,MAAA,CACP4C,SAAS,EAAIA,SAAS,CAACvC,OAAO,sBAAAL,MAAA,CAAwB4C,SAAS,CAACxC,OAAO,OAAAJ,MAAA,CAAK4C,SAAS,CAACvC,OAAO,EAAK,EAAE,CAAE,qFACxG,mGAAAL,MAAA,CACc+D,UAAU,4BAAA/D,MAAA,CAA0BI,OAAO,4BAA0B,0FAC9E,sHAAAJ,MAAA,CAC4BI,OAAO,SAAO,kBAAAJ,MAAA,CAClHiD,IAAI,EAAI,WAAW,CAAE,CAExC,MAAO,CACLpB,OAAO,CAAE,IAAI,CACb6B,WAAW,CAAEa,MAAM,CACnB1B,YAAY,CAAE,IAChB,CAAC,CACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}