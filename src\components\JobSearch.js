import React, { useState, useEffect, useCallback } from 'react';
import { collection, onSnapshot, query } from 'firebase/firestore';
import { Search, Briefcase, MapPin, ExternalLink, LinkIcon, AlertCircle, RefreshCw } from 'lucide-react';
import { Section, InputField, ActionButton, LoadingSpinner } from './UIComponents';
import { searchJobs, searchJobsAlternative } from '../services/jobSearchService';
import { validateSearchKeywords, validateLocation } from '../utils/validation';
import ComponentErrorBoundary from './ComponentErrorBoundary';

function JobSearch({ userId, db, appId }) {
  const [keywords, setKeywords] = useState('');
  const [location, setLocation] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [sites, setSites] = useState([]);
  const [validationErrors, setValidationErrors] = useState({});
  const [retryCount, setRetryCount] = useState(0);
  const [lastSearchParams, setLastSearchParams] = useState(null);

  const sitesCollectionRef = userId ? collection(db, `artifacts/${appId}/users/${userId}/jobSites`) : null;

  // Load user's job sites
  useEffect(() => {
    if (!sitesCollectionRef) return;
    const unsubscribe = onSnapshot(query(sitesCollectionRef), (snapshot) => {
      const sitesData = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      setSites(sitesData);
    }, (err) => {
      console.error("Error fetching sites for search: ", err);
    });
    return () => unsubscribe();
  }, [userId, sitesCollectionRef, db, appId]);

  // Memoized search function to prevent unnecessary re-renders
  const performSearch = useCallback(async (searchKeywords, searchLocation, searchSites) => {
    try {
      // Use web scraping approach with our server
      const result = await searchJobs(searchKeywords, searchLocation, searchSites);

      if (result.success) {
        setSearchResults(result.data || []);
        setRetryCount(0); // Reset retry count on success

        if (result.data && result.data.length === 0) {
          if (result.message) {
            setError(result.message);
          } else {
            setError("No jobs found matching your criteria. Try adjusting your search terms.");
          }
        }
        // Show a message if using mock data
        if (result.message && result.data.length > 0 && result.message.includes('mock data')) {
          setError(result.message);
        }
      } else {
        // If web scraping failed, try fallback approach
        const fallbackResult = await searchJobsAlternative(searchKeywords, searchLocation, searchSites);

        if (fallbackResult.success && fallbackResult.data && fallbackResult.data.length > 0) {
          setSearchResults(fallbackResult.data);
          setError(`Note: Web scraping failed. Using fallback data. ${fallbackResult.message || ''}`);
        } else {
          throw new Error(result.error || fallbackResult.error || "Failed to search for jobs");
        }
      }
    } catch (error) {
      console.error("Job search error:", error);
      setError(`Error searching for jobs: ${error.message}. Please try again.`);
      setRetryCount(prev => prev + 1);
    }
  }, []);

  // Handle search with improved error handling
  const handleSearch = async (e) => {
    e?.preventDefault();

    // Validate inputs
    const keywordsValidation = validateSearchKeywords(keywords);
    const locationValidation = validateLocation(location);

    const errors = {};
    if (!keywordsValidation.isValid) {
      errors.keywords = keywordsValidation.error;
    }
    if (!locationValidation.isValid) {
      errors.location = locationValidation.error;
    }

    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      return;
    }

    if (!keywords && !location) {
      setError("Please enter keywords or location to search for jobs.");
      return;
    }

    setIsLoading(true);
    setError('');
    setSearchResults([]);
    setValidationErrors({});

    // Use sanitized inputs
    const sanitizedKeywords = keywordsValidation.sanitized || keywords;
    const sanitizedLocation = locationValidation.sanitized || location;

    // Store search params for retry functionality
    setLastSearchParams({ keywords: sanitizedKeywords, location: sanitizedLocation, sites });

    try {
      await performSearch(sanitizedKeywords, sanitizedLocation, sites);
    } finally {
      setIsLoading(false);
    }
  };

  // Retry last search
  const handleRetry = useCallback(async () => {
    if (lastSearchParams) {
      setIsLoading(true);
      setError('');
      try {
        await performSearch(lastSearchParams.keywords, lastSearchParams.location, lastSearchParams.sites);
      } finally {
        setIsLoading(false);
      }
    }
  }, [lastSearchParams, performSearch]);
  };

  return (
    <ComponentErrorBoundary
      componentName="Job Search"
      errorMessage="There was an error with the job search functionality."
    >
      <Section title="Find Your Next Opportunity" icon={<Search className="w-5 h-5" />}>
        {error && (
          <div className="mb-4 p-3 bg-red-500/20 text-red-300 border border-red-500 rounded-md">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <p>{error}</p>
                {retryCount > 0 && (
                  <p className="text-xs text-red-400 mt-1">
                    Retry attempt: {retryCount}
                  </p>
                )}
              </div>
              {lastSearchParams && retryCount < 3 && (
                <button
                  onClick={handleRetry}
                  disabled={isLoading}
                  className="ml-3 px-3 py-1 bg-red-600 hover:bg-red-500 disabled:bg-red-800 text-white text-xs rounded transition-colors flex items-center"
                >
                  <RefreshCw className="w-3 h-3 mr-1" />
                  Retry
                </button>
              )}
            </div>
          </div>
        )}
      <div className="info-alert mb-4 p-3 bg-blue-500/20 text-blue-300 border border-blue-500/50 rounded-md flex items-start">
        <AlertCircle size={20} className="mr-2 mt-0.5 flex-shrink-0" />
        <p className="text-sm">
          This application uses web scraping to find real job listings from your saved job sites. Results may take a moment to load.
        </p>
      </div>
      <form onSubmit={handleSearch} className="space-y-4 mb-6 p-4 bg-slate-800 rounded-lg shadow-md">
        <InputField 
          label="Keywords" 
          type="text" 
          value={keywords} 
          onChange={(e) => {
            setKeywords(e.target.value);
            if (validationErrors.keywords) {
              setValidationErrors({...validationErrors, keywords: null});
            }
          }} 
          placeholder="e.g., React Developer, Marketing Manager"
          error={validationErrors.keywords}
          required
        />
        <InputField 
          label="Location" 
          type="text" 
          value={location} 
          onChange={(e) => {
            setLocation(e.target.value);
            if (validationErrors.location) {
              setValidationErrors({...validationErrors, location: null});
            }
          }} 
          placeholder="e.g., Remote, New York, London"
          error={validationErrors.location}
        />
        <ActionButton type="submit" icon={<Search />} disabled={isLoading}>
          {isLoading ? 'Searching...' : 'Search Jobs'}
        </ActionButton>
      </form>

      {isLoading ? <LoadingSpinner /> : (
        searchResults.length > 0 ? (
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-sky-300 mb-3">Search Results ({searchResults.length})</h3>
            {searchResults.map((job) => (
              <div key={job.id} className="p-4 bg-slate-700/50 rounded-lg shadow hover:shadow-xl transition-shadow duration-300">
                <h4 className="text-lg font-semibold text-sky-400">{job.title}</h4>
                <p className="text-slate-300"><Briefcase size={14} className="inline mr-2" />{job.company}</p>
                <p className="text-slate-400 text-sm"><MapPin size={14} className="inline mr-2" />{job.location}</p>
                <p className="text-xs text-slate-500 mt-1">Source: {job.sourceSite}</p>
                <div className="mt-3 flex flex-col sm:flex-row gap-2">
                  <a href={job.url} target="_blank" rel="noopener noreferrer" className="flex-1 text-center px-4 py-2 text-sm font-medium rounded-md bg-sky-600 hover:bg-sky-500 text-white transition-colors shadow hover:shadow-md flex items-center justify-center">
                    View Job <ExternalLink size={14} className="ml-2"/>
                  </a>
                  <ActionButton
                    variant="secondary"
                    className="flex-1"
                    disabled
                    title="Auto-fill application feature coming soon!"
                  >
                    Apply Now
                  </ActionButton>
                </div>
              </div>
            ))}
          </div>
        ) : (
          !error && <p className="text-slate-500 italic text-center">Enter keywords or location to start your job search.</p>
        )
      )}
      
      {sites.length > 0 && (
        <div className="mt-8 p-4 bg-slate-800/70 rounded-lg">
          <h4 className="text-md font-semibold text-sky-400 mb-2">Searching on:</h4>
          <ul className="flex flex-wrap gap-2">
            {sites.map(site => (
              <li key={site.id} className="px-3 py-1 bg-slate-700 text-xs text-slate-300 rounded-full flex items-center">
                <LinkIcon size={12} className="mr-1.5 text-sky-500"/> {site.name}
              </li>
            ))}
          </ul>
          {sites.length > 0 && (
            <p className="text-xs text-slate-500 mt-3">
              <AlertCircle size={12} className="inline-block mr-1" />
              Note: Web scraping may be blocked by some job sites. If results are limited, try adding more job sites or different search terms.
            </p>
          )}
        </div>
      )}
    </Section>
    </ComponentErrorBoundary>
  );
}

export default JobSearch;
