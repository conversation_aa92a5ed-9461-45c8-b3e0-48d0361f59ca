{"ast": null, "code": "/**\n * lucide-react v0.279.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst CloudCog = createLucideIcon(\"CloudCog\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"17\",\n  r: \"3\",\n  key: \"1spfwm\"\n}], [\"path\", {\n  d: \"M4.2 15.1A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.2\",\n  key: \"zaobp\"\n}], [\"path\", {\n  d: \"m15.7 18.4-.9-.3\",\n  key: \"4qxpbn\"\n}], [\"path\", {\n  d: \"m9.2 15.9-.9-.3\",\n  key: \"17q7o2\"\n}], [\"path\", {\n  d: \"m10.6 20.7.3-.9\",\n  key: \"1pf4s2\"\n}], [\"path\", {\n  d: \"m13.1 14.2.3-.9\",\n  key: \"1mnuqm\"\n}], [\"path\", {\n  d: \"m13.6 20.7-.4-1\",\n  key: \"1jpd1m\"\n}], [\"path\", {\n  d: \"m10.8 14.3-.4-1\",\n  key: \"17ugyy\"\n}], [\"path\", {\n  d: \"m8.3 18.6 1-.4\",\n  key: \"s42vdx\"\n}], [\"path\", {\n  d: \"m14.7 15.8 1-.4\",\n  key: \"2wizun\"\n}]]);\nexport { CloudCog as default };", "map": {"version": 3, "names": ["CloudCog", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\Users\\<USER>\\Documents\\Repo\\Auto_Job_Application\\node_modules\\lucide-react\\src\\icons\\cloud-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CloudCog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjE3IiByPSIzIiAvPgogIDxwYXRoIGQ9Ik00LjIgMTUuMUE3IDcgMCAxIDEgMTUuNzEgOGgxLjc5YTQuNSA0LjUgMCAwIDEgMi41IDguMiIgLz4KICA8cGF0aCBkPSJtMTUuNyAxOC40LS45LS4zIiAvPgogIDxwYXRoIGQ9Im05LjIgMTUuOS0uOS0uMyIgLz4KICA8cGF0aCBkPSJtMTAuNiAyMC43LjMtLjkiIC8+CiAgPHBhdGggZD0ibTEzLjEgMTQuMi4zLS45IiAvPgogIDxwYXRoIGQ9Im0xMy42IDIwLjctLjQtMSIgLz4KICA8cGF0aCBkPSJtMTAuOCAxNC4zLS40LTEiIC8+CiAgPHBhdGggZD0ibTguMyAxOC42IDEtLjQiIC8+CiAgPHBhdGggZD0ibTE0LjcgMTUuOCAxLS40IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/cloud-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CloudCog = createLucideIcon('CloudCog', [\n  ['circle', { cx: '12', cy: '17', r: '3', key: '1spfwm' }],\n  [\n    'path',\n    {\n      d: 'M4.2 15.1A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.2',\n      key: 'zaobp',\n    },\n  ],\n  ['path', { d: 'm15.7 18.4-.9-.3', key: '4qxpbn' }],\n  ['path', { d: 'm9.2 15.9-.9-.3', key: '17q7o2' }],\n  ['path', { d: 'm10.6 20.7.3-.9', key: '1pf4s2' }],\n  ['path', { d: 'm13.1 14.2.3-.9', key: '1mnuqm' }],\n  ['path', { d: 'm13.6 20.7-.4-1', key: '1jpd1m' }],\n  ['path', { d: 'm10.8 14.3-.4-1', key: '17ugyy' }],\n  ['path', { d: 'm8.3 18.6 1-.4', key: 's42vdx' }],\n  ['path', { d: 'm14.7 15.8 1-.4', key: '2wizun' }],\n]);\n\nexport default CloudCog;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CACE,QACA;EACEC,CAAG;EACHD,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,EACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}