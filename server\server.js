const express = require('express');
const cors = require('cors');
const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Configuration
const config = {
  port: process.env.PORT || 5000,
  corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 10,
  rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 60000,
  puppeteerTimeout: parseInt(process.env.PUPPETEER_TIMEOUT) || 30000,
  maxConcurrentScrapes: parseInt(process.env.MAX_CONCURRENT_SCRAPES) || 3,
  scrapeTimeout: parseInt(process.env.SCRAPE_TIMEOUT) || 45000,
  retryAttempts: parseInt(process.env.RETRY_ATTEMPTS) || 2,
  logLevel: process.env.LOG_LEVEL || 'info',
  enableRequestLogging: process.env.ENABLE_REQUEST_LOGGING === 'true'
};

// Rate limiting with improved cleanup
const rateLimitMap = new Map();
let lastCleanup = Date.now();
const CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes

// Concurrent scraping control
let activeScrapes = 0;
const scrapeQueue = [];

const app = express();

// Improved rate limiting middleware with efficient cleanup
const rateLimit = (maxRequests = config.rateLimitMaxRequests, windowMs = config.rateLimitWindowMs) => {
  return (req, res, next) => {
    const clientIp = req.ip || req.connection.remoteAddress || 'unknown';
    const now = Date.now();
    const windowStart = now - windowMs;

    // Periodic cleanup to prevent memory leaks
    if (now - lastCleanup > CLEANUP_INTERVAL) {
      cleanupRateLimitMap(windowStart);
      lastCleanup = now;
    }

    // Check current client
    const clientRequests = rateLimitMap.get(clientIp) || [];
    const recentRequests = clientRequests.filter(time => time > windowStart);

    if (recentRequests.length >= maxRequests) {
      const resetTime = Math.ceil((recentRequests[0] + windowMs - now) / 1000);
      logRequest(req, `Rate limit exceeded for IP: ${clientIp}`);
      return res.status(429).json({
        success: false,
        error: `Rate limit exceeded. Try again in ${resetTime} seconds.`,
        data: [],
        retryAfter: resetTime
      });
    }

    recentRequests.push(now);
    rateLimitMap.set(clientIp, recentRequests);
    next();
  };
};

// Efficient cleanup function
const cleanupRateLimitMap = (windowStart) => {
  for (const [ip, requests] of rateLimitMap.entries()) {
    const validRequests = requests.filter(time => time > windowStart);
    if (validRequests.length === 0) {
      rateLimitMap.delete(ip);
    } else {
      rateLimitMap.set(ip, validRequests);
    }
  }
};

// Logging utility
const logRequest = (req, message, level = 'info') => {
  if (config.enableRequestLogging) {
    const timestamp = new Date().toISOString();
    const method = req.method;
    const url = req.url;
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    console.log(`[${timestamp}] ${level.toUpperCase()}: ${method} ${url} - IP: ${ip} - ${message}`);
  }
};

// Enhanced input sanitization and validation middleware
const sanitizeInput = (req, res, next) => {
  if (req.body) {
    for (const key in req.body) {
      if (typeof req.body[key] === 'string') {
        req.body[key] = req.body[key]
          .replace(/<script[^>]*>.*?<\/script>/gi, '')
          .replace(/<[^>]*>/g, '')
          .replace(/javascript:/gi, '')
          .replace(/vbscript:/gi, '')
          .replace(/data:text\/html/gi, '')
          .replace(/on\w+\s*=/gi, '')
          .trim();
      }
    }
  }
  next();
};

// Server-side validation for job search
const validateJobSearchInput = (req, res, next) => {
  const { keywords, location, sites } = req.body;

  // Validate keywords
  if (!keywords || typeof keywords !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'Keywords are required and must be a string',
      data: []
    });
  }

  if (keywords.trim().length < 2) {
    return res.status(400).json({
      success: false,
      error: 'Keywords must be at least 2 characters long',
      data: []
    });
  }

  if (keywords.length > 100) {
    return res.status(400).json({
      success: false,
      error: 'Keywords must be less than 100 characters',
      data: []
    });
  }

  // Validate location (optional)
  if (location && (typeof location !== 'string' || location.length > 50)) {
    return res.status(400).json({
      success: false,
      error: 'Location must be a string with less than 50 characters',
      data: []
    });
  }

  // Validate sites (optional)
  if (sites && (!Array.isArray(sites) || sites.length > 10)) {
    return res.status(400).json({
      success: false,
      error: 'Sites must be an array with maximum 10 items',
      data: []
    });
  }

  next();
};

// Middleware
app.use(cors({
  origin: config.corsOrigin,
  credentials: true,
  optionsSuccessStatus: 200
}));
app.use(express.json({ limit: '1mb' })); // Reduced limit for security
app.use(sanitizeInput);
app.use(express.static(path.join(__dirname, '../build')));

// Request logging middleware
if (config.enableRequestLogging) {
  app.use((req, res, next) => {
    logRequest(req, `${req.method} ${req.url}`);
    next();
  });
}

// Concurrency control for scraping
const queueScrapeJob = (scrapeFunction) => {
  return new Promise((resolve, reject) => {
    const job = { scrapeFunction, resolve, reject };

    if (activeScrapes < config.maxConcurrentScrapes) {
      executeScrapeJob(job);
    } else {
      scrapeQueue.push(job);
    }
  });
};

const executeScrapeJob = async (job) => {
  activeScrapes++;
  try {
    const result = await job.scrapeFunction();
    job.resolve(result);
  } catch (error) {
    job.reject(error);
  } finally {
    activeScrapes--;
    if (scrapeQueue.length > 0) {
      const nextJob = scrapeQueue.shift();
      executeScrapeJob(nextJob);
    }
  }
};

// Enhanced job search endpoint with improved error handling
app.post('/api/scrape-jobs', rateLimit(5, 60000), validateJobSearchInput, async (req, res) => {
  const { keywords, location, sites } = req.body;
  const startTime = Date.now();

  logRequest(req, `Job search request: keywords="${keywords}", location="${location || 'N/A'}"`);

  // Set timeout for the entire request
  const requestTimeout = setTimeout(() => {
    if (!res.headersSent) {
      res.status(408).json({
        success: false,
        error: 'Request timeout. Please try again with fewer sites or simpler search terms.',
        data: []
      });
    }
  }, config.scrapeTimeout);
  
  try {
    // If no sites are provided, use default job boards
    const jobSites = sites?.length > 0 ? sites : [
      { name: 'Indeed', url: 'https://www.indeed.com' },
      { name: 'LinkedIn', url: 'https://www.linkedin.com/jobs' }
    ];

    // Collect all job results with improved error handling
    const allJobs = [];
    const errors = [];
    let jobId = 1;

    // Process sites with concurrency control
    const scrapePromises = jobSites.map(async (site) => {
      try {
        const scrapeFunction = async () => {
          let jobs = [];

          if (site.name.toLowerCase().includes('indeed')) {
            jobs = await scrapeIndeedWithRetry(keywords, location);
          } else if (site.name.toLowerCase().includes('linkedin')) {
            jobs = await scrapeLinkedInWithRetry(keywords, location);
          } else {
            jobs = await scrapeGenericWithRetry(site.url, keywords, location);
          }

          return jobs;
        };

        const jobs = await queueScrapeJob(scrapeFunction);

        // Add source site and unique IDs
        jobs.forEach(job => {
          job.id = `${site.name.toLowerCase().replace(/\s+/g, '-')}-${jobId++}`;
          job.sourceSite = site.name;
          job.scrapedAt = new Date().toISOString();
        });

        return jobs;

      } catch (error) {
        logRequest(req, `Error scraping ${site.name}: ${error.message}`, 'error');
        errors.push(`${site.name}: ${error.message}`);
        return [];
      }
    });

    // Wait for all scraping to complete
    const results = await Promise.allSettled(scrapePromises);

    // Collect successful results
    results.forEach((result) => {
      if (result.status === 'fulfilled') {
        allJobs.push(...result.value);
      }
    });

    clearTimeout(requestTimeout);

    const duration = Date.now() - startTime;
    logRequest(req, `Job search completed in ${duration}ms. Found ${allJobs.length} jobs.`);

    // Prepare response
    let message = null;
    if (allJobs.length === 0) {
      message = errors.length > 0
        ? `No jobs found. Some sites encountered errors: ${errors.join(', ')}`
        : 'No jobs found matching your criteria.';
    } else if (errors.length > 0) {
      message = `Found ${allJobs.length} jobs. Some sites had issues: ${errors.join(', ')}`;
    }

    return res.json({
      success: true,
      data: allJobs,
      message,
      meta: {
        totalResults: allJobs.length,
        searchDuration: duration,
        sitesSearched: jobSites.length,
        sitesWithErrors: errors.length
      }
    });

  } catch (error) {
    clearTimeout(requestTimeout);
    logRequest(req, `Job search failed: ${error.message}`, 'error');

    if (!res.headersSent) {
      return res.status(500).json({
        success: false,
        error: 'Failed to search for jobs. Please try again later.',
        data: []
      });
    }
  }
});

// Retry wrapper for scraping functions
async function withRetry(scrapeFunction, maxRetries = config.retryAttempts) {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await scrapeFunction();
    } catch (error) {
      lastError = error;
      if (attempt < maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
}

// Retry-enabled scraping functions
async function scrapeIndeedWithRetry(keywords, location) {
  return withRetry(() => scrapeIndeed(keywords, location));
}

async function scrapeLinkedInWithRetry(keywords, location) {
  return withRetry(() => scrapeLinkedIn(keywords, location));
}

async function scrapeGenericWithRetry(siteUrl, keywords, location) {
  return withRetry(() => scrapeGeneric(siteUrl, keywords, location));
}

// Enhanced browser configuration
const getBrowserConfig = () => ({
  headless: process.env.PUPPETEER_HEADLESS !== 'false',
  args: [
    '--no-sandbox',
    '--disable-setuid-sandbox',
    '--disable-dev-shm-usage',
    '--disable-accelerated-2d-canvas',
    '--no-first-run',
    '--no-zygote',
    '--disable-gpu',
    '--disable-background-timer-throttling',
    '--disable-backgrounding-occluded-windows',
    '--disable-renderer-backgrounding'
  ],
  timeout: config.puppeteerTimeout
});

// Helper function to scrape Indeed with enhanced error handling
async function scrapeIndeed(keywords, location) {
  let browser;
  try {
    // Launch headless browser with enhanced configuration
    browser = await puppeteer.launch(getBrowserConfig());
    
    const page = await browser.newPage();

    // Enhanced page configuration
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
    await page.setViewport({ width: 1366, height: 768 });

    // Set extra headers to appear more like a real browser
    await page.setExtraHTTPHeaders({
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1'
    });

    // Construct search URL with better encoding
    const baseUrl = 'https://www.indeed.com/jobs';
    const params = new URLSearchParams();
    params.append('q', keywords);
    if (location) {
      params.append('l', location);
    }
    const searchUrl = `${baseUrl}?${params.toString()}`;

    // Navigate to search results with timeout
    await page.goto(searchUrl, {
      waitUntil: 'networkidle2',
      timeout: config.puppeteerTimeout
    });

    // Wait for job listings to load with multiple selectors
    const selectors = ['.jobsearch-ResultsList', '.job_seen_beacon', '.slider_container'];
    let selectorFound = false;

    for (const selector of selectors) {
      try {
        await page.waitForSelector(selector, { timeout: 5000 });
        selectorFound = true;
        break;
      } catch (e) {
        // Try next selector
      }
    }

    if (!selectorFound) {
      throw new Error('Could not find job results on Indeed');
    }
    
    // Extract job data
    const jobs = await page.evaluate(() => {
      const results = [];
      
      // Get all job cards
      const jobCards = document.querySelectorAll('.job_seen_beacon');
      
      jobCards.forEach(card => {
        try {
          // Extract job details
          const titleElement = card.querySelector('h2.jobTitle > a');
          const companyElement = card.querySelector('.companyName');
          const locationElement = card.querySelector('.companyLocation');
          const snippetElement = card.querySelector('.job-snippet');
          
          if (!titleElement) return;
          
          const title = titleElement.innerText.trim();
          const url = 'https://www.indeed.com' + titleElement.getAttribute('href');
          const company = companyElement ? companyElement.innerText.trim() : 'Unknown Company';
          const location = locationElement ? locationElement.innerText.trim() : 'Unknown Location';
          const description = snippetElement ? snippetElement.innerText.trim() : '';
          
          results.push({
            title,
            company,
            location,
            description,
            url
          });
        } catch (e) {
          // Skip this job card if errors occur during extraction
        }
      });
      
      return results;
    });
    
    await browser.close();
    return jobs;
  } catch (error) {
    console.error('Error scraping Indeed:', error);
    throw new Error(`Indeed scraping failed: ${error.message}`);
  } finally {
    if (browser) {
      await browser.close().catch(e => console.error('Error closing browser:', e));
    }
  }
}

// Helper function to scrape LinkedIn
async function scrapeLinkedIn(keywords, location) {
  try {
    // Launch headless browser
    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Set user agent to avoid being blocked
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    
    // Construct search URL
    let searchUrl = 'https://www.linkedin.com/jobs/search/?keywords=' + encodeURIComponent(keywords);
    if (location) {
      searchUrl += '&location=' + encodeURIComponent(location);
    }
    
    // Navigate to search results
    await page.goto(searchUrl, { waitUntil: 'networkidle2' });
    
    // Wait for job listings to load
    await page.waitForSelector('.jobs-search__results-list', { timeout: 5000 })
      .catch(() => console.log('Could not find job results list selector'));
    
    // Extract job data
    const jobs = await page.evaluate(() => {
      const results = [];
      
      // Get all job cards
      const jobCards = document.querySelectorAll('.jobs-search__results-list > li');
      
      jobCards.forEach(card => {
        try {
          // Extract job details
          const titleElement = card.querySelector('.base-search-card__title');
          const linkElement = card.querySelector('a.base-card__full-link');
          const companyElement = card.querySelector('.base-search-card__subtitle');
          const locationElement = card.querySelector('.job-search-card__location');
          
          if (!titleElement || !linkElement) return;
          
          const title = titleElement.innerText.trim();
          const url = linkElement.getAttribute('href');
          const company = companyElement ? companyElement.innerText.trim() : 'Unknown Company';
          const location = locationElement ? locationElement.innerText.trim() : 'Unknown Location';
          
          results.push({
            title,
            company,
            location,
            description: '',  // LinkedIn doesn't show descriptions in the search list
            url
          });
        } catch (e) {
          // Skip this job card if errors occur during extraction
        }
      });
      
      return results;
    });
    
    await browser.close();
    return jobs;
  } catch (error) {
    console.error('Error scraping LinkedIn:', error);
    return [];
  }
}

// Helper function for generic job site scraping
async function scrapeGeneric(siteUrl, keywords, location) {
  try {
    // Launch headless browser
    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Set user agent to avoid being blocked
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    
    // Navigate to the job site
    await page.goto(siteUrl, { waitUntil: 'networkidle2' });
    
    // Look for search inputs and attempt to fill them
    await page.evaluate(async (keywords, location) => {
      // Common selectors for search inputs
      const keywordSelectors = [
        'input[name="q"]', 'input[name="keywords"]', 'input[placeholder*="job"]', 
        'input[placeholder*="search"]', 'input[placeholder*="keyword"]'
      ];
      
      const locationSelectors = [
        'input[name="l"]', 'input[name="location"]', 'input[placeholder*="location"]', 
        'input[placeholder*="city"]', 'input[placeholder*="where"]'
      ];
      
      // Try to find keyword input
      for (const selector of keywordSelectors) {
        const input = document.querySelector(selector);
        if (input) {
          input.value = keywords;
          break;
        }
      }
      
      // Try to find location input
      if (location) {
        for (const selector of locationSelectors) {
          const input = document.querySelector(selector);
          if (input) {
            input.value = location;
            break;
          }
        }
      }
      
      // Try to find the search button and click it
      const searchButtons = document.querySelectorAll('button, input[type="submit"]');
      for (const button of searchButtons) {
        const text = button.innerText?.toLowerCase() || '';
        if (text.includes('search') || text.includes('find') || text.includes('go')) {
          button.click();
          return true;
        }
      }
      
      return false;
    }, keywords, location);
    
    // Wait for potential navigation and give page time to load
    await page.waitForTimeout(5000);
    
    // Extract anything that looks like a job listing
    const jobs = await page.evaluate(() => {
      const results = [];
      
      // Look for common job listing containers
      const containers = document.querySelectorAll('div.job, .job-card, .job-listing, .job-result, .search-result');
      
      containers.forEach(container => {
        try {
          // Look for title element
          let titleElement = container.querySelector('h2, h3, .title, .job-title');
          if (!titleElement) return;
          
          let linkElement = container.querySelector('a');
          let companyElement = container.querySelector('.company, .employer, .organization');
          let locationElement = container.querySelector('.location, .job-location, .address');
          let descriptionElement = container.querySelector('.description, .summary, .snippet');
          
          const title = titleElement.innerText.trim();
          const url = linkElement ? linkElement.href : '';
          const company = companyElement ? companyElement.innerText.trim() : 'Unknown Company';
          const location = locationElement ? locationElement.innerText.trim() : 'Unknown Location';
          const description = descriptionElement ? descriptionElement.innerText.trim() : '';
          
          results.push({
            title,
            company,
            location,
            description,
            url
          });
        } catch (e) {
          // Skip this container if errors occur during extraction
        }
      });
      
      return results;
    });
    
    await browser.close();
    return jobs;
  } catch (error) {
    console.error('Error scraping generic site:', error);
    return [];
  }
}

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    activeScrapes,
    queueLength: scrapeQueue.length
  });
});

// API status endpoint
app.get('/api/status', (req, res) => {
  res.json({
    success: true,
    message: 'Job Assistant API is running',
    endpoints: {
      health: '/api/health',
      scrapeJobs: '/api/scrape-jobs',
      status: '/api/status'
    },
    rateLimit: {
      maxRequests: config.rateLimitMaxRequests,
      windowMs: config.rateLimitWindowMs
    }
  });
});

// Fallback route for SPA
app.get('*', (req, res) => {
  const indexPath = path.join(__dirname, '../build', 'index.html');
  fs.readFile(indexPath, 'utf8', (err, html) => {
    if (err) {
      console.error('Error reading index.html:', err);
      return res.status(500).send('Error loading the application.');
    }

    const initialAuthToken = process.env.INITIAL_AUTH_TOKEN || null;
    console.log('Server: INITIAL_AUTH_TOKEN read as:', initialAuthToken);

    // Inject the global variable into the HTML
    const modifiedHtml = html.replace(
      '<div id="root"></div>',
      `<script>window.__initial_auth_token = ${JSON.stringify(initialAuthToken)};</script><div id="root"></div>`
    );

    res.send(modifiedHtml);
  });
});

// Graceful shutdown handling
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server with enhanced logging
const server = app.listen(config.port, () => {
  console.log(`🚀 Job Assistant Server started successfully!`);
  console.log(`📍 Server running on port ${config.port}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 Health check: http://localhost:${config.port}/api/health`);
  console.log(`📊 API status: http://localhost:${config.port}/api/status`);
  console.log(`🎯 CORS origin: ${config.corsOrigin}`);
  console.log(`⏱️  Request timeout: ${config.puppeteerTimeout}ms`);
  console.log(`🔄 Max concurrent scrapes: ${config.maxConcurrentScrapes}`);

  if (config.enableRequestLogging) {
    console.log(`📝 Request logging: enabled`);
  }
});

// Handle server errors
server.on('error', (error) => {
  console.error('Server error:', error);
  process.exit(1);
});
