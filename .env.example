# Firebase Configuration
# Get these values from your Firebase Console: https://console.firebase.google.com/
REACT_APP_FIREBASE_API_KEY=your_firebase_api_key_here
REACT_APP_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your_project_id
REACT_APP_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
REACT_APP_FIREBASE_APP_ID=your_firebase_app_id

# Google AI API Key (Optional - for AI-generated summaries)
# Get from: https://ai.google.dev/
REACT_APP_GOOGLE_AI_API_KEY=your_gemini_api_key_here

# Application Configuration
REACT_APP_APP_ID=job-assistant-app-v1
REACT_APP_SERVER_URL=http://localhost:5000
REACT_APP_REQUEST_TIMEOUT=30000
REACT_APP_RETRY_ATTEMPTS=3

# Development Settings
NODE_ENV=development
REACT_APP_ENABLE_DEBUG=true
