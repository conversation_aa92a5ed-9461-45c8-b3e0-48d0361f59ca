# Server Configuration
PORT=5000
NODE_ENV=development

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=10
RATE_LIMIT_WINDOW_MS=60000

# Puppeteer Configuration
PUPPETEER_HEADLESS=true
PUPPETEER_TIMEOUT=30000

# Security
INITIAL_AUTH_TOKEN=

# Logging
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true

# Performance
MAX_CONCURRENT_SCRAPES=3
SCRAPE_TIMEOUT=45000
RETRY_ATTEMPTS=2
