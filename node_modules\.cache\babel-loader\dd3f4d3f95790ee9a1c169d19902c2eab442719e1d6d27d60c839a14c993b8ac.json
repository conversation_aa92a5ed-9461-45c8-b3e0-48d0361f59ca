{"ast": null, "code": "/**\n * lucide-react v0.279.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Briefcase = createLucideIcon(\"Briefcase\", [[\"rect\", {\n  width: \"20\",\n  height: \"14\",\n  x: \"2\",\n  y: \"7\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"eto64e\"\n}], [\"path\", {\n  d: \"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16\",\n  key: \"zwj3tp\"\n}]]);\nexport { Briefcase as default };", "map": {"version": 3, "names": ["Briefcase", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "d"], "sources": ["C:\\Users\\<USER>\\Documents\\Repo\\Auto_Job_Application\\node_modules\\lucide-react\\src\\icons\\briefcase.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Briefcase\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTQiIHg9IjIiIHk9IjciIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNMTYgMjFWNWEyIDIgMCAwIDAtMi0yaC00YTIgMiAwIDAgMC0yIDJ2MTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/briefcase\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Briefcase = createLucideIcon('Briefcase', [\n  [\n    'rect',\n    {\n      width: '20',\n      height: '14',\n      x: '2',\n      y: '7',\n      rx: '2',\n      ry: '2',\n      key: 'eto64e',\n    },\n  ],\n  ['path', { d: 'M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16', key: 'zwj3tp' }],\n]);\n\nexport default Briefcase;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CACE,QACA;EACEC,KAAO;EACPC,MAAQ;EACRC,CAAG;EACHC,CAAG;EACHC,EAAI;EACJC,EAAI;EACJC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,4CAA8C;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}