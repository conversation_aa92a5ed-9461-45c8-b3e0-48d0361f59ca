{"ast": null, "code": "/**\n * lucide-react v0.279.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Files = createLucideIcon(\"Files\", [[\"path\", {\n  d: \"M15.5 2H8.6c-.4 0-.8.2-1.1.5-.3.3-.5.7-.5 1.1v12.8c0 .******* *******.7.5 1.1.5h9.8c.4 0 .8-.2 1.1-.5.3-.3.5-.7.5-1.1V6.5L15.5 2z\",\n  key: \"cennsq\"\n}], [\"path\", {\n  d: \"M3 7.6v12.8c0 .******* *******.7.5 1.1.5h9.8\",\n  key: \"ms809a\"\n}], [\"path\", {\n  d: \"M15 2v5h5\",\n  key: \"qq6kwv\"\n}]]);\nexport { Files as default };", "map": {"version": 3, "names": ["Files", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Documents\\Repo\\Auto_Job_Application\\node_modules\\lucide-react\\src\\icons\\files.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Files\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) - https://lucide.dev/icons/files\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Files = createLucideIcon('Files', [\n  [\n    'path',\n    {\n      d: 'M15.5 2H8.6c-.4 0-.8.2-1.1.5-.3.3-.5.7-.5 1.1v12.8c0 .******* *******.7.5 1.1.5h9.8c.4 0 .8-.2 1.1-.5.3-.3.5-.7.5-1.1V6.5L15.5 2z',\n      key: 'cennsq',\n    },\n  ],\n  [\n    'path',\n    { d: 'M3 7.6v12.8c0 .******* *******.7.5 1.1.5h9.8', key: 'ms809a' },\n  ],\n  ['path', { d: 'M15 2v5h5', key: 'qq6kwv' }],\n]);\n\nexport default Files;\n"], "mappings": ";;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EAAED,CAAA,EAAG,8CAAgD;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}