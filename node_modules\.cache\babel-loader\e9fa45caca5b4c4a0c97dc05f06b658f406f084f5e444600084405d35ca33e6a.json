{"ast": null, "code": "import utils from '../utils.js';\nimport httpAdapter from './http.js';\nimport xhrAdapter from './xhr.js';\nimport fetchAdapter from './fetch.js';\nimport AxiosError from \"../core/AxiosError.js\";\nconst knownAdapters = {\n  http: httpAdapter,\n  xhr: xhrAdapter,\n  fetch: fetchAdapter\n};\nutils.forEach(knownAdapters, (fn, value) => {\n  if (fn) {\n    try {\n      Object.defineProperty(fn, 'name', {\n        value\n      });\n    } catch (e) {\n      // eslint-disable-next-line no-empty\n    }\n    Object.defineProperty(fn, 'adapterName', {\n      value\n    });\n  }\n});\nconst renderReason = reason => \"- \".concat(reason);\nconst isResolvedHandle = adapter => utils.isFunction(adapter) || adapter === null || adapter === false;\nexport default {\n  getAdapter: adapters => {\n    adapters = utils.isArray(adapters) ? adapters : [adapters];\n    const {\n      length\n    } = adapters;\n    let nameOrAdapter;\n    let adapter;\n    const rejectedReasons = {};\n    for (let i = 0; i < length; i++) {\n      nameOrAdapter = adapters[i];\n      let id;\n      adapter = nameOrAdapter;\n      if (!isResolvedHandle(nameOrAdapter)) {\n        adapter = knownAdapters[(id = String(nameOrAdapter)).toLowerCase()];\n        if (adapter === undefined) {\n          throw new AxiosError(\"Unknown adapter '\".concat(id, \"'\"));\n        }\n      }\n      if (adapter) {\n        break;\n      }\n      rejectedReasons[id || '#' + i] = adapter;\n    }\n    if (!adapter) {\n      const reasons = Object.entries(rejectedReasons).map(_ref => {\n        let [id, state] = _ref;\n        return \"adapter \".concat(id, \" \") + (state === false ? 'is not supported by the environment' : 'is not available in the build');\n      });\n      let s = length ? reasons.length > 1 ? 'since :\\n' + reasons.map(renderReason).join('\\n') : ' ' + renderReason(reasons[0]) : 'as no adapter specified';\n      throw new AxiosError(\"There is no suitable adapter to dispatch the request \" + s, 'ERR_NOT_SUPPORT');\n    }\n    return adapter;\n  },\n  adapters: knownAdapters\n};", "map": {"version": 3, "names": ["utils", "httpAdapter", "xhrAdapter", "fetchAdapter", "AxiosError", "knownAdapters", "http", "xhr", "fetch", "for<PERSON>ach", "fn", "value", "Object", "defineProperty", "e", "renderReason", "reason", "concat", "isResolvedHandle", "adapter", "isFunction", "getAdapter", "adapters", "isArray", "length", "nameOrAdapter", "rejectedReasons", "i", "id", "String", "toLowerCase", "undefined", "reasons", "entries", "map", "_ref", "state", "s", "join"], "sources": ["C:/Users/<USER>/Documents/Repo/Auto_Job_Application/node_modules/axios/lib/adapters/adapters.js"], "sourcesContent": ["import utils from '../utils.js';\nimport httpAdapter from './http.js';\nimport xhrAdapter from './xhr.js';\nimport fetchAdapter from './fetch.js';\nimport AxiosError from \"../core/AxiosError.js\";\n\nconst knownAdapters = {\n  http: httpAdapter,\n  xhr: xhrAdapter,\n  fetch: fetchAdapter\n}\n\nutils.forEach(knownAdapters, (fn, value) => {\n  if (fn) {\n    try {\n      Object.defineProperty(fn, 'name', {value});\n    } catch (e) {\n      // eslint-disable-next-line no-empty\n    }\n    Object.defineProperty(fn, 'adapterName', {value});\n  }\n});\n\nconst renderReason = (reason) => `- ${reason}`;\n\nconst isResolvedHandle = (adapter) => utils.isFunction(adapter) || adapter === null || adapter === false;\n\nexport default {\n  getAdapter: (adapters) => {\n    adapters = utils.isArray(adapters) ? adapters : [adapters];\n\n    const {length} = adapters;\n    let nameOrAdapter;\n    let adapter;\n\n    const rejectedReasons = {};\n\n    for (let i = 0; i < length; i++) {\n      nameOrAdapter = adapters[i];\n      let id;\n\n      adapter = nameOrAdapter;\n\n      if (!isResolvedHandle(nameOrAdapter)) {\n        adapter = knownAdapters[(id = String(nameOrAdapter)).toLowerCase()];\n\n        if (adapter === undefined) {\n          throw new AxiosError(`Unknown adapter '${id}'`);\n        }\n      }\n\n      if (adapter) {\n        break;\n      }\n\n      rejectedReasons[id || '#' + i] = adapter;\n    }\n\n    if (!adapter) {\n\n      const reasons = Object.entries(rejectedReasons)\n        .map(([id, state]) => `adapter ${id} ` +\n          (state === false ? 'is not supported by the environment' : 'is not available in the build')\n        );\n\n      let s = length ?\n        (reasons.length > 1 ? 'since :\\n' + reasons.map(renderReason).join('\\n') : ' ' + renderReason(reasons[0])) :\n        'as no adapter specified';\n\n      throw new AxiosError(\n        `There is no suitable adapter to dispatch the request ` + s,\n        'ERR_NOT_SUPPORT'\n      );\n    }\n\n    return adapter;\n  },\n  adapters: knownAdapters\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,WAAW,MAAM,WAAW;AACnC,OAAOC,UAAU,MAAM,UAAU;AACjC,OAAOC,YAAY,MAAM,YAAY;AACrC,OAAOC,UAAU,MAAM,uBAAuB;AAE9C,MAAMC,aAAa,GAAG;EACpBC,IAAI,EAAEL,WAAW;EACjBM,GAAG,EAAEL,UAAU;EACfM,KAAK,EAAEL;AACT,CAAC;AAEDH,KAAK,CAACS,OAAO,CAACJ,aAAa,EAAE,CAACK,EAAE,EAAEC,KAAK,KAAK;EAC1C,IAAID,EAAE,EAAE;IACN,IAAI;MACFE,MAAM,CAACC,cAAc,CAACH,EAAE,EAAE,MAAM,EAAE;QAACC;MAAK,CAAC,CAAC;IAC5C,CAAC,CAAC,OAAOG,CAAC,EAAE;MACV;IAAA;IAEFF,MAAM,CAACC,cAAc,CAACH,EAAE,EAAE,aAAa,EAAE;MAACC;IAAK,CAAC,CAAC;EACnD;AACF,CAAC,CAAC;AAEF,MAAMI,YAAY,GAAIC,MAAM,SAAAC,MAAA,CAAUD,MAAM,CAAE;AAE9C,MAAME,gBAAgB,GAAIC,OAAO,IAAKnB,KAAK,CAACoB,UAAU,CAACD,OAAO,CAAC,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK;AAExG,eAAe;EACbE,UAAU,EAAGC,QAAQ,IAAK;IACxBA,QAAQ,GAAGtB,KAAK,CAACuB,OAAO,CAACD,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IAE1D,MAAM;MAACE;IAAM,CAAC,GAAGF,QAAQ;IACzB,IAAIG,aAAa;IACjB,IAAIN,OAAO;IAEX,MAAMO,eAAe,GAAG,CAAC,CAAC;IAE1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,EAAEG,CAAC,EAAE,EAAE;MAC/BF,aAAa,GAAGH,QAAQ,CAACK,CAAC,CAAC;MAC3B,IAAIC,EAAE;MAENT,OAAO,GAAGM,aAAa;MAEvB,IAAI,CAACP,gBAAgB,CAACO,aAAa,CAAC,EAAE;QACpCN,OAAO,GAAGd,aAAa,CAAC,CAACuB,EAAE,GAAGC,MAAM,CAACJ,aAAa,CAAC,EAAEK,WAAW,CAAC,CAAC,CAAC;QAEnE,IAAIX,OAAO,KAAKY,SAAS,EAAE;UACzB,MAAM,IAAI3B,UAAU,qBAAAa,MAAA,CAAqBW,EAAE,MAAG,CAAC;QACjD;MACF;MAEA,IAAIT,OAAO,EAAE;QACX;MACF;MAEAO,eAAe,CAACE,EAAE,IAAI,GAAG,GAAGD,CAAC,CAAC,GAAGR,OAAO;IAC1C;IAEA,IAAI,CAACA,OAAO,EAAE;MAEZ,MAAMa,OAAO,GAAGpB,MAAM,CAACqB,OAAO,CAACP,eAAe,CAAC,CAC5CQ,GAAG,CAACC,IAAA;QAAA,IAAC,CAACP,EAAE,EAAEQ,KAAK,CAAC,GAAAD,IAAA;QAAA,OAAK,WAAAlB,MAAA,CAAWW,EAAE,UAChCQ,KAAK,KAAK,KAAK,GAAG,qCAAqC,GAAG,+BAA+B,CAAC;MAAA,CAC7F,CAAC;MAEH,IAAIC,CAAC,GAAGb,MAAM,GACXQ,OAAO,CAACR,MAAM,GAAG,CAAC,GAAG,WAAW,GAAGQ,OAAO,CAACE,GAAG,CAACnB,YAAY,CAAC,CAACuB,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAGvB,YAAY,CAACiB,OAAO,CAAC,CAAC,CAAC,CAAC,GACzG,yBAAyB;MAE3B,MAAM,IAAI5B,UAAU,CAClB,0DAA0DiC,CAAC,EAC3D,iBACF,CAAC;IACH;IAEA,OAAOlB,OAAO;EAChB,CAAC;EACDG,QAAQ,EAAEjB;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}