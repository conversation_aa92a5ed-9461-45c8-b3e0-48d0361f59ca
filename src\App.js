import React, { useState, useEffect } from 'react';
import { initializeApp } from 'firebase/app';
import { getAuth, signInAnonymously, onAuthStateChanged, signInWithCustomToken } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

// Import components
import UserProfile from './components/UserProfile';
import JobSites from './components/JobSites';
import JobSearch from './components/JobSearch';
import Footer from './components/Footer';
import LoadingScreen from './components/LoadingScreen';
import SettingsPage from './components/SettingsPage'; // Import the new SettingsPage component

// Import Firebase configuration
import { firebaseConfig, appId, isDemoMode } from './firebaseConfig';

// Initialize Firebase with error handling
let app, auth, db;
try {
  app = initializeApp(firebaseConfig);
  auth = getAuth(app);
  db = getFirestore(app);
} catch (error) {
  console.error("Firebase initialization error:", error);
  // We'll handle this error in the App component
}

// Main App Component
function App() {
  const [currentPage, setCurrentPage] = useState('search'); // 'profile', 'sites', 'search', 'settings'
  const [userId, setUserId] = useState(null);
  const [isAuthReady, setIsAuthReady] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [firebaseError, setFirebaseError] = useState(null);

  // Firebase Auth State Listener
  useEffect(() => {
    // Check if Firebase was initialized properly
    if (!app || !auth || !db) {
      setFirebaseError("Firebase initialization failed. Please check your configuration.");
      setIsLoading(false);
      return;
    }

    // Set up auth state listener
    const initialAuthToken = (typeof window !== 'undefined' && window.__initial_auth_token) || null;
    if (process.env.NODE_ENV === 'development') {
      console.log('Initial auth token:', initialAuthToken ? 'Available' : 'Not available');
    }
    
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        setUserId(user.uid);
        setIsAuthReady(true);
        setIsLoading(false);
      } else {
        try {
          if (initialAuthToken) {
            try {
              await signInWithCustomToken(auth, initialAuthToken);
            } catch (error) {
              console.error("Custom token sign-in failed, falling back to anonymous sign-in: ", error);
              await signInAnonymously(auth);
            }
          } else {
            await signInAnonymously(auth);
          }
        } catch (error) {
          console.error("Error signing in: ", error);
          
          // Handle specific Firebase auth errors
          let errorMessage = "Authentication error: ";
          if (error.code === 'auth/admin-restricted-operation') {
            errorMessage = "Anonymous authentication is not enabled in Firebase. Please enable it in your Firebase Console under Authentication > Sign-in method > Anonymous.";
          } else if (error.code === 'auth/operation-not-allowed') {
            errorMessage = "This authentication method is not enabled in Firebase. Please check your Firebase Console settings.";
          } else if (error.code === 'auth/network-request-failed') {
            errorMessage = "Network error. Please check your internet connection and try again.";
          } else {
            errorMessage += error.message;
          }
          
          setFirebaseError(errorMessage);
          setIsAuthReady(true);
          setIsLoading(false);
        }
      }
    }, (error) => {
      console.error("Auth state change error:", error);
      setFirebaseError("Authentication error: " + error.message);
      setIsLoading(false);
    });
    
    return () => unsubscribe();
  }, []);

  // Display loading screen
  if (isLoading) {
    return <LoadingScreen message="Initializing Job Assistant..." />;
  }
  
  // Display Firebase configuration error
  if (firebaseError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-slate-100 font-sans flex flex-col items-center justify-center p-8 text-center">
        <div className="bg-red-500/20 border border-red-500 p-6 rounded-lg max-w-xl">
          <h2 className="text-2xl font-bold text-red-400 mb-4">Configuration Error</h2>
          <p className="text-slate-300 mb-6">{firebaseError}</p>
          <div className="bg-slate-800/50 p-4 rounded-lg text-left">
            <p className="text-slate-400 mb-2">To fix this:</p>
            {firebaseError.includes('Anonymous authentication is not enabled') ? (
              <ol className="list-decimal list-inside text-slate-400 space-y-2">
                <li>Go to <a href="https://console.firebase.google.com/" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">Firebase Console</a></li>
                <li>Select your Firebase project</li>
                <li>Navigate to Authentication → Sign-in method</li>
                <li>Enable "Anonymous" authentication</li>
                <li>Refresh this page</li>
              </ol>
            ) : isDemoMode ? (
              <ol className="list-decimal list-inside text-slate-400 space-y-2">
                <li>Create a <code className="bg-slate-700/50 px-2 py-1 rounded">.env</code> file in your project root</li>
                <li>Copy the contents from <code className="bg-slate-700/50 px-2 py-1 rounded">.env.example</code></li>
                <li>Replace the placeholder values with your actual Firebase credentials</li>
                <li>Restart the application</li>
              </ol>
            ) : (
              <ol className="list-decimal list-inside text-slate-400 space-y-2">
                <li>Check your environment variables</li>
                <li>Ensure all required Firebase configuration is set</li>
                <li>Restart the application</li>
              </ol>
            )}
          </div>
          {isDemoMode && (
            <div className="mt-4 p-3 bg-yellow-500/20 border border-yellow-500 rounded-lg">
              <p className="text-yellow-300 text-sm">
                <strong>Demo Mode:</strong> You're running with demo configuration.
                Set up your Firebase project for full functionality.
              </p>
            </div>
          )}
        </div>
      </div>
    );
  }
  
  // Display authentication screen
  if (!isAuthReady) {
    return <LoadingScreen message="Authenticating..." />;
  }

  const renderPage = () => {
    switch (currentPage) {
      case 'profile':
        return <UserProfile userId={userId} db={db} appId={appId} />;
      case 'sites':
        return <JobSites userId={userId} db={db} appId={appId} />;
      case 'search':
        return <JobSearch userId={userId} db={db} appId={appId} />;
      case 'settings': // Add the new settings page case
        return <SettingsPage userId={userId} db={db} appId={appId} />;
      default:
        return <UserProfile userId={userId} db={db} appId={appId} />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-slate-100 font-sans flex flex-col items-center p-4 sm:p-6 md:p-8 relative overflow-hidden">
      {/* Background decoration elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-blue-500/10 rounded-full filter blur-3xl -z-10 animate-pulse"></div>
      <div className="absolute bottom-0 left-0 w-80 h-80 bg-purple-500/10 rounded-full filter blur-3xl -z-10 animate-pulse" style={{animationDuration: '8s'}}></div>
      
      <header className="w-full max-w-4xl mb-10 text-center">
        <div className="relative inline-block">
          <h1 className="text-5xl sm:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-sky-400 via-indigo-400 to-purple-500 animate-gradient-x">
            AI Job Assistant
          </h1>
          <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-gradient-to-r from-sky-400 to-purple-500 rounded-full"></div>
        </div>
        <p className="text-slate-300 mt-4 text-lg">Your smart companion for navigating the job market.</p>
        {userId && <p className="text-xs text-slate-500 mt-2 bg-slate-800/50 inline-block px-2 py-1 rounded-full">User ID: {userId}</p>}
      </header>

      <nav className="w-full max-w-md mb-10 bg-slate-800/70 backdrop-blur-lg p-2 rounded-xl shadow-[0_8px_30px_rgb(0,0,0,0.12)] border border-slate-700/50 flex justify-around transition-all duration-300 hover:shadow-[0_8px_30px_rgb(59,130,246,0.1)] animate-fade-in">
        {['profile', 'sites', 'search', 'settings'].map((page) => (
          <button
            key={page}
            onClick={() => setCurrentPage(page)}
            className={`px-5 py-2.5 rounded-lg text-sm font-medium transition-all duration-300 ease-in-out relative overflow-hidden
              ${currentPage === page
                ? 'bg-gradient-to-r from-sky-500 to-blue-600 text-white shadow-lg shadow-blue-500/20'
                : 'text-slate-300 hover:bg-slate-700/70 hover:text-sky-300'
              }
            `}
          >
            {currentPage === page && (
              <div className="absolute inset-0 bg-white/20 animate-shine"></div>
            )}
            {page.charAt(0).toUpperCase() + page.slice(1)}
          </button>
        ))}
      </nav>

      <main className="w-full max-w-4xl animate-fade-in-up">
        {renderPage()}
      </main>
      
      <Footer />
    </div>
  );
}

export default App;
