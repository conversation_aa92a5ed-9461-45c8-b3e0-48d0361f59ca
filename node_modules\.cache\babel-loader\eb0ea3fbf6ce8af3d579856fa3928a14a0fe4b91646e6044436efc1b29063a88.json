{"ast": null, "code": "/**\n * lucide-react v0.279.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BookOpenCheck = createLucideIcon(\"BookOpenCheck\", [[\"path\", {\n  d: \"M8 3H2v15h7c1.7 0 3 1.3 3 3V7c0-2.2-1.8-4-4-4Z\",\n  key: \"1i8u0n\"\n}], [\"path\", {\n  d: \"m16 12 2 2 4-4\",\n  key: \"mdajum\"\n}], [\"path\", {\n  d: \"M22 6V3h-6c-2.2 0-4 1.8-4 4v14c0-1.7 1.3-3 3-3h7v-2.3\",\n  key: \"jb5l51\"\n}]]);\nexport { BookOpenCheck as default };", "map": {"version": 3, "names": ["BookOpenCheck", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Documents\\Repo\\Auto_Job_Application\\node_modules\\lucide-react\\src\\icons\\book-open-check.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BookOpenCheck\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAzSDJ2MTVoN2MxLjcgMCAzIDEuMyAzIDNWN2MwLTIuMi0xLjgtNC00LTRaIiAvPgogIDxwYXRoIGQ9Im0xNiAxMiAyIDIgNC00IiAvPgogIDxwYXRoIGQ9Ik0yMiA2VjNoLTZjLTIuMiAwLTQgMS44LTQgNHYxNGMwLTEuNyAxLjMtMyAzLTNoN3YtMi4zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/book-open-check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookOpenCheck = createLucideIcon('BookOpenCheck', [\n  [\n    'path',\n    { d: 'M8 3H2v15h7c1.7 0 3 1.3 3 3V7c0-2.2-1.8-4-4-4Z', key: '1i8u0n' },\n  ],\n  ['path', { d: 'm16 12 2 2 4-4', key: 'mdajum' }],\n  [\n    'path',\n    {\n      d: 'M22 6V3h-6c-2.2 0-4 1.8-4 4v14c0-1.7 1.3-3 3-3h7v-2.3',\n      key: 'jb5l51',\n    },\n  ],\n]);\n\nexport default BookOpenCheck;\n"], "mappings": ";;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CACE,QACA;EAAEC,CAAA,EAAG,gDAAkD;EAAAC,GAAA,EAAK;AAAS,EACvE,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}