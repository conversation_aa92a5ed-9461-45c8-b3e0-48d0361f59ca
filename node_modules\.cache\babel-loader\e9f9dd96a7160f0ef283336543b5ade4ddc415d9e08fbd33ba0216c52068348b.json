{"ast": null, "code": "/**\n * lucide-react v0.279.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BookDown = createLucideIcon(\"BookDown\", [[\"path\", {\n  d: \"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20\",\n  key: \"t4utmx\"\n}], [\"path\", {\n  d: \"M12 13V7\",\n  key: \"h0r20n\"\n}], [\"path\", {\n  d: \"m9 10 3 3 3-3\",\n  key: \"zt5b4y\"\n}]]);\nexport { BookDown as default };", "map": {"version": 3, "names": ["BookDown", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Documents\\Repo\\Auto_Job_Application\\node_modules\\lucide-react\\src\\icons\\book-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BookDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxOS41di0xNUEyLjUgMi41IDAgMCAxIDYuNSAySDIwdjIwSDYuNWEyLjUgMi41IDAgMCAxIDAtNUgyMCIgLz4KICA8cGF0aCBkPSJNMTIgMTNWNyIgLz4KICA8cGF0aCBkPSJtOSAxMCAzIDMgMy0zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/book-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookDown = createLucideIcon('BookDown', [\n  [\n    'path',\n    {\n      d: 'M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20',\n      key: 't4utmx',\n    },\n  ],\n  ['path', { d: 'M12 13V7', key: 'h0r20n' }],\n  ['path', { d: 'm9 10 3 3 3-3', key: 'zt5b4y' }],\n]);\n\nexport default BookDown;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}