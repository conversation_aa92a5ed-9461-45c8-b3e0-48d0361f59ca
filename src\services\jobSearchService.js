import axios from 'axios';
import { serverConfig } from '../firebaseConfig';

// This service handles the job search functionality
// It uses web scraping instead of APIs to fetch real job listings

// Configuration
const config = {
  serverUrl: serverConfig.url,
  timeout: serverConfig.timeout,
  retryAttempts: serverConfig.retryAttempts,
  retryDelay: 1000 // Base delay for exponential backoff
};

// Create axios instance with default configuration
const apiClient = axios.create({
  baseURL: config.serverUrl,
  timeout: config.timeout,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Retry utility function
const withRetry = async (fn, maxRetries = config.retryAttempts) => {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;

      // Don't retry on client errors (4xx) except for 408 (timeout) and 429 (rate limit)
      if (error.response && error.response.status >= 400 && error.response.status < 500) {
        if (error.response.status !== 408 && error.response.status !== 429) {
          throw error;
        }
      }

      if (attempt < maxRetries) {
        const delay = config.retryDelay * Math.pow(2, attempt - 1); // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
};

/**
 * Search for jobs using web scraping through our server-side endpoint
 * @param {string} keywords - Job search keywords/title
 * @param {string} location - Job location
 * @param {Array} sites - Array of job site objects {name, url}
 * @returns {Object} - Search results or error
 */
export const searchJobs = async (keywords, location, sites = []) => {
  // Input validation
  if (!keywords || typeof keywords !== 'string' || keywords.trim().length < 2) {
    return {
      success: false,
      error: 'Keywords are required and must be at least 2 characters long',
      data: []
    };
  }

  if (location && (typeof location !== 'string' || location.length > 50)) {
    return {
      success: false,
      error: 'Location must be a string with less than 50 characters',
      data: []
    };
  }

  if (sites && (!Array.isArray(sites) || sites.length > 10)) {
    return {
      success: false,
      error: 'Sites must be an array with maximum 10 items',
      data: []
    };
  }

  try {
    // Make request with retry logic
    const response = await withRetry(async () => {
      return await apiClient.post('/api/scrape-jobs', {
        keywords: keywords.trim(),
        location: location?.trim() || '',
        sites
      });
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching jobs:', error);

    // Enhanced error handling
    let errorMessage = 'Failed to fetch jobs';
    let shouldRetry = false;

    if (error.response) {
      // Server responded with error status
      const status = error.response.status;
      const serverError = error.response.data?.error;

      if (status === 429) {
        errorMessage = serverError || 'Too many requests. Please wait before trying again.';
        shouldRetry = true;
      } else if (status === 408) {
        errorMessage = 'Request timeout. The job search is taking longer than expected.';
        shouldRetry = true;
      } else if (status >= 500) {
        errorMessage = 'Server error. Please try again later.';
        shouldRetry = true;
      } else if (status >= 400) {
        errorMessage = serverError || 'Invalid request. Please check your search parameters.';
      }
    } else if (error.request) {
      // Network error
      errorMessage = 'Unable to connect to the job search service. Please check your internet connection.';
      shouldRetry = true;
    } else {
      // Other error
      errorMessage = error.message || 'An unexpected error occurred';
    }

    return {
      success: false,
      error: errorMessage,
      data: [],
      shouldRetry,
      retryAfter: error.response?.data?.retryAfter
    };
  }
};

// Alternative implementation for when the scraping server is unavailable
export const searchJobsAlternative = async (keywords, location, sites = []) => {
  try {
    // First try the web scraping approach
    const result = await searchJobs(keywords, location, sites);
    if (result.success) {
      return result;
    }
    
    // If web scraping failed, use mock data
    console.log('Web scraping failed, using mock data');
    
    const mockJobs = [
      {
        id: 'mock-1',
        title: `${keywords} Developer`,
        company: 'Tech Solutions Inc',
        location: location || 'Remote',
        description: `Looking for a talented ${keywords} developer to join our team. Great opportunity to work with cutting-edge technology.`,
        url: '#',
        sourceSite: 'Mock Data'
      },
      {
        id: 'mock-2',
        title: `Senior ${keywords} Engineer`,
        company: 'InnovateCorp',
        location: location || 'Remote',
        description: `Seeking experienced ${keywords} professional for a leadership role in our growing engineering team.`,
        url: '#',
        sourceSite: 'Mock Data'
      },
      {
        id: 'mock-3',
        title: `${keywords} Specialist`,
        company: 'Global Tech Partners',
        location: location || 'Remote',
        description: `Join our international team as a ${keywords} specialist. Competitive salary and benefits package.`,
        url: '#',
        sourceSite: 'Mock Data'
      }
    ];
    
    return {
      success: true,
      data: mockJobs,
      message: 'Note: Using mock data as web scraping is currently unavailable. To use real job data, ensure the server is running.'
    };
  } catch (error) {
    console.error('Error in alternative job search:', error);
    
    return {
      success: false,
      error: error.message || 'Failed to search for jobs',
      data: [] // Always include data property even in error case for consistent API
    };
  }
};
