{"ast": null, "code": "// Input validation and sanitization utilities\n\n// Sanitize string input to prevent XSS\nexport const sanitizeString = str => {\n  if (typeof str !== 'string') return '';\n  return str.replace(/<script[^>]*>.*?<\\/script>/gi, '') // Remove script tags\n  .replace(/<[^>]*>/g, '') // Remove HTML tags\n  .replace(/javascript:/gi, '') // Remove javascript: protocol\n  .replace(/on\\w+\\s*=/gi, '') // Remove event handlers like onclick=\n  .trim();\n};\n\n// Validate email format\nexport const validateEmail = email => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\n// Validate URL format\nexport const validateUrl = url => {\n  try {\n    const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);\n    return ['http:', 'https:'].includes(urlObj.protocol);\n  } catch {\n    return false;\n  }\n};\n\n// Validate and sanitize search keywords\nexport const validateSearchKeywords = keywords => {\n  if (!keywords || typeof keywords !== 'string') {\n    return {\n      isValid: false,\n      error: 'Keywords are required'\n    };\n  }\n  const sanitized = sanitizeString(keywords);\n  if (sanitized.length < 2) {\n    return {\n      isValid: false,\n      error: 'Keywords must be at least 2 characters long'\n    };\n  }\n  if (sanitized.length > 100) {\n    return {\n      isValid: false,\n      error: 'Keywords must be less than 100 characters'\n    };\n  }\n\n  // Check for suspicious patterns\n  const suspiciousPatterns = [/\\b(select|insert|update|delete|drop|union|script)\\b/i, /[<>'\"&]/];\n  for (const pattern of suspiciousPatterns) {\n    if (pattern.test(sanitized)) {\n      return {\n        isValid: false,\n        error: 'Keywords contain invalid characters'\n      };\n    }\n  }\n  return {\n    isValid: true,\n    sanitized\n  };\n};\n\n// Validate location input\nexport const validateLocation = location => {\n  if (!location) {\n    return {\n      isValid: true,\n      sanitized: ''\n    }; // Location is optional\n  }\n  if (typeof location !== 'string') {\n    return {\n      isValid: false,\n      error: 'Invalid location format'\n    };\n  }\n  const sanitized = sanitizeString(location);\n  if (sanitized.length > 50) {\n    return {\n      isValid: false,\n      error: 'Location must be less than 50 characters'\n    };\n  }\n  return {\n    isValid: true,\n    sanitized\n  };\n};\n\n// Validate name input\nexport const validateName = name => {\n  if (!name || typeof name !== 'string') {\n    return {\n      isValid: false,\n      error: 'Name is required'\n    };\n  }\n  const sanitized = sanitizeString(name);\n  if (sanitized.length < 1) {\n    return {\n      isValid: false,\n      error: 'Name cannot be empty'\n    };\n  }\n  if (sanitized.length > 100) {\n    return {\n      isValid: false,\n      error: 'Name must be less than 100 characters'\n    };\n  }\n\n  // Only allow letters, spaces, hyphens, and apostrophes\n  const nameRegex = /^[a-zA-Z\\s\\-'.]+$/;\n  if (!nameRegex.test(sanitized)) {\n    return {\n      isValid: false,\n      error: 'Name contains invalid characters'\n    };\n  }\n  return {\n    isValid: true,\n    sanitized\n  };\n};\n\n// Validate skills input\nexport const validateSkills = skills => {\n  if (!skills) {\n    return {\n      isValid: true,\n      sanitized: ''\n    }; // Skills are optional\n  }\n  if (typeof skills !== 'string') {\n    return {\n      isValid: false,\n      error: 'Invalid skills format'\n    };\n  }\n  const sanitized = sanitizeString(skills);\n  if (sanitized.length > 500) {\n    return {\n      isValid: false,\n      error: 'Skills must be less than 500 characters'\n    };\n  }\n  return {\n    isValid: true,\n    sanitized\n  };\n};\n\n// Validate job site name\nexport const validateSiteName = name => {\n  if (!name || typeof name !== 'string') {\n    return {\n      isValid: false,\n      error: 'Site name is required'\n    };\n  }\n  const sanitized = sanitizeString(name);\n  if (sanitized.length < 1) {\n    return {\n      isValid: false,\n      error: 'Site name cannot be empty'\n    };\n  }\n  if (sanitized.length > 50) {\n    return {\n      isValid: false,\n      error: 'Site name must be less than 50 characters'\n    };\n  }\n  return {\n    isValid: true,\n    sanitized\n  };\n};\n\n// Rate limiting helper\nexport const createRateLimiter = (maxRequests, windowMs) => {\n  const requests = new Map();\n  return identifier => {\n    const now = Date.now();\n    const windowStart = now - windowMs;\n\n    // Clean old entries\n    for (const [key, timestamps] of requests.entries()) {\n      const validTimestamps = timestamps.filter(t => t > windowStart);\n      if (validTimestamps.length === 0) {\n        requests.delete(key);\n      } else {\n        requests.set(key, validTimestamps);\n      }\n    }\n\n    // Check current identifier\n    const userRequests = requests.get(identifier) || [];\n    const recentRequests = userRequests.filter(t => t > windowStart);\n    if (recentRequests.length >= maxRequests) {\n      return {\n        allowed: false,\n        resetTime: Math.ceil((recentRequests[0] + windowMs - now) / 1000)\n      };\n    }\n    recentRequests.push(now);\n    requests.set(identifier, recentRequests);\n    return {\n      allowed: true\n    };\n  };\n};", "map": {"version": 3, "names": ["sanitizeString", "str", "replace", "trim", "validateEmail", "email", "emailRegex", "test", "validateUrl", "url", "url<PERSON>bj", "URL", "startsWith", "includes", "protocol", "validateSearchKeywords", "keywords", "<PERSON><PERSON><PERSON><PERSON>", "error", "sanitized", "length", "suspiciousPatterns", "pattern", "validateLocation", "location", "validateName", "name", "nameRegex", "validateSkills", "skills", "validateSiteName", "createRateLimiter", "maxRequests", "windowMs", "requests", "Map", "identifier", "now", "Date", "windowStart", "key", "timestamps", "entries", "validTimestamps", "filter", "t", "delete", "set", "userRequests", "get", "recentRequests", "allowed", "resetTime", "Math", "ceil", "push"], "sources": ["C:/Users/<USER>/Documents/Repo/Auto_Job_Application/src/utils/validation.js"], "sourcesContent": ["// Input validation and sanitization utilities\n\n// Sanitize string input to prevent XSS\nexport const sanitizeString = (str) => {\n  if (typeof str !== 'string') return '';\n  \n  return str\n    .replace(/<script[^>]*>.*?<\\/script>/gi, '') // Remove script tags\n    .replace(/<[^>]*>/g, '') // Remove HTML tags\n    .replace(/javascript:/gi, '') // Remove javascript: protocol\n    .replace(/on\\w+\\s*=/gi, '') // Remove event handlers like onclick=\n    .trim();\n};\n\n// Validate email format\nexport const validateEmail = (email) => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\n// Validate URL format\nexport const validateUrl = (url) => {\n  try {\n    const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);\n    return ['http:', 'https:'].includes(urlObj.protocol);\n  } catch {\n    return false;\n  }\n};\n\n// Validate and sanitize search keywords\nexport const validateSearchKeywords = (keywords) => {\n  if (!keywords || typeof keywords !== 'string') {\n    return { isValid: false, error: 'Keywords are required' };\n  }\n\n  const sanitized = sanitizeString(keywords);\n  \n  if (sanitized.length < 2) {\n    return { isValid: false, error: 'Keywords must be at least 2 characters long' };\n  }\n  \n  if (sanitized.length > 100) {\n    return { isValid: false, error: 'Keywords must be less than 100 characters' };\n  }\n\n  // Check for suspicious patterns\n  const suspiciousPatterns = [\n    /\\b(select|insert|update|delete|drop|union|script)\\b/i,\n    /[<>'\"&]/\n  ];\n  \n  for (const pattern of suspiciousPatterns) {\n    if (pattern.test(sanitized)) {\n      return { isValid: false, error: 'Keywords contain invalid characters' };\n    }\n  }\n\n  return { isValid: true, sanitized };\n};\n\n// Validate location input\nexport const validateLocation = (location) => {\n  if (!location) {\n    return { isValid: true, sanitized: '' }; // Location is optional\n  }\n\n  if (typeof location !== 'string') {\n    return { isValid: false, error: 'Invalid location format' };\n  }\n\n  const sanitized = sanitizeString(location);\n  \n  if (sanitized.length > 50) {\n    return { isValid: false, error: 'Location must be less than 50 characters' };\n  }\n\n  return { isValid: true, sanitized };\n};\n\n// Validate name input\nexport const validateName = (name) => {\n  if (!name || typeof name !== 'string') {\n    return { isValid: false, error: 'Name is required' };\n  }\n\n  const sanitized = sanitizeString(name);\n  \n  if (sanitized.length < 1) {\n    return { isValid: false, error: 'Name cannot be empty' };\n  }\n  \n  if (sanitized.length > 100) {\n    return { isValid: false, error: 'Name must be less than 100 characters' };\n  }\n\n  // Only allow letters, spaces, hyphens, and apostrophes\n  const nameRegex = /^[a-zA-Z\\s\\-'.]+$/;\n  if (!nameRegex.test(sanitized)) {\n    return { isValid: false, error: 'Name contains invalid characters' };\n  }\n\n  return { isValid: true, sanitized };\n};\n\n// Validate skills input\nexport const validateSkills = (skills) => {\n  if (!skills) {\n    return { isValid: true, sanitized: '' }; // Skills are optional\n  }\n\n  if (typeof skills !== 'string') {\n    return { isValid: false, error: 'Invalid skills format' };\n  }\n\n  const sanitized = sanitizeString(skills);\n  \n  if (sanitized.length > 500) {\n    return { isValid: false, error: 'Skills must be less than 500 characters' };\n  }\n\n  return { isValid: true, sanitized };\n};\n\n// Validate job site name\nexport const validateSiteName = (name) => {\n  if (!name || typeof name !== 'string') {\n    return { isValid: false, error: 'Site name is required' };\n  }\n\n  const sanitized = sanitizeString(name);\n  \n  if (sanitized.length < 1) {\n    return { isValid: false, error: 'Site name cannot be empty' };\n  }\n  \n  if (sanitized.length > 50) {\n    return { isValid: false, error: 'Site name must be less than 50 characters' };\n  }\n\n  return { isValid: true, sanitized };\n};\n\n// Rate limiting helper\nexport const createRateLimiter = (maxRequests, windowMs) => {\n  const requests = new Map();\n  \n  return (identifier) => {\n    const now = Date.now();\n    const windowStart = now - windowMs;\n    \n    // Clean old entries\n    for (const [key, timestamps] of requests.entries()) {\n      const validTimestamps = timestamps.filter(t => t > windowStart);\n      if (validTimestamps.length === 0) {\n        requests.delete(key);\n      } else {\n        requests.set(key, validTimestamps);\n      }\n    }\n    \n    // Check current identifier\n    const userRequests = requests.get(identifier) || [];\n    const recentRequests = userRequests.filter(t => t > windowStart);\n    \n    if (recentRequests.length >= maxRequests) {\n      return { allowed: false, resetTime: Math.ceil((recentRequests[0] + windowMs - now) / 1000) };\n    }\n    \n    recentRequests.push(now);\n    requests.set(identifier, recentRequests);\n    \n    return { allowed: true };\n  };\n};\n"], "mappings": "AAAA;;AAEA;AACA,OAAO,MAAMA,cAAc,GAAIC,GAAG,IAAK;EACrC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,OAAO,EAAE;EAEtC,OAAOA,GAAG,CACPC,OAAO,CAAC,8BAA8B,EAAE,EAAE,CAAC,CAAC;EAAA,CAC5CA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;EAAA,CACxBA,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;EAAA,CAC7BA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;EAAA,CAC3BC,IAAI,CAAC,CAAC;AACX,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAIC,KAAK,IAAK;EACtC,MAAMC,UAAU,GAAG,4BAA4B;EAC/C,OAAOA,UAAU,CAACC,IAAI,CAACF,KAAK,CAAC;AAC/B,CAAC;;AAED;AACA,OAAO,MAAMG,WAAW,GAAIC,GAAG,IAAK;EAClC,IAAI;IACF,MAAMC,MAAM,GAAG,IAAIC,GAAG,CAACF,GAAG,CAACG,UAAU,CAAC,MAAM,CAAC,GAAGH,GAAG,GAAG,WAAWA,GAAG,EAAE,CAAC;IACvE,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACI,QAAQ,CAACH,MAAM,CAACI,QAAQ,CAAC;EACtD,CAAC,CAAC,MAAM;IACN,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,sBAAsB,GAAIC,QAAQ,IAAK;EAClD,IAAI,CAACA,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAC7C,OAAO;MAAEC,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAwB,CAAC;EAC3D;EAEA,MAAMC,SAAS,GAAGnB,cAAc,CAACgB,QAAQ,CAAC;EAE1C,IAAIG,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACxB,OAAO;MAAEH,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAA8C,CAAC;EACjF;EAEA,IAAIC,SAAS,CAACC,MAAM,GAAG,GAAG,EAAE;IAC1B,OAAO;MAAEH,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAA4C,CAAC;EAC/E;;EAEA;EACA,MAAMG,kBAAkB,GAAG,CACzB,sDAAsD,EACtD,SAAS,CACV;EAED,KAAK,MAAMC,OAAO,IAAID,kBAAkB,EAAE;IACxC,IAAIC,OAAO,CAACf,IAAI,CAACY,SAAS,CAAC,EAAE;MAC3B,OAAO;QAAEF,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAsC,CAAC;IACzE;EACF;EAEA,OAAO;IAAED,OAAO,EAAE,IAAI;IAAEE;EAAU,CAAC;AACrC,CAAC;;AAED;AACA,OAAO,MAAMI,gBAAgB,GAAIC,QAAQ,IAAK;EAC5C,IAAI,CAACA,QAAQ,EAAE;IACb,OAAO;MAAEP,OAAO,EAAE,IAAI;MAAEE,SAAS,EAAE;IAAG,CAAC,CAAC,CAAC;EAC3C;EAEA,IAAI,OAAOK,QAAQ,KAAK,QAAQ,EAAE;IAChC,OAAO;MAAEP,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAA0B,CAAC;EAC7D;EAEA,MAAMC,SAAS,GAAGnB,cAAc,CAACwB,QAAQ,CAAC;EAE1C,IAAIL,SAAS,CAACC,MAAM,GAAG,EAAE,EAAE;IACzB,OAAO;MAAEH,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAA2C,CAAC;EAC9E;EAEA,OAAO;IAAED,OAAO,EAAE,IAAI;IAAEE;EAAU,CAAC;AACrC,CAAC;;AAED;AACA,OAAO,MAAMM,YAAY,GAAIC,IAAI,IAAK;EACpC,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACrC,OAAO;MAAET,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAmB,CAAC;EACtD;EAEA,MAAMC,SAAS,GAAGnB,cAAc,CAAC0B,IAAI,CAAC;EAEtC,IAAIP,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACxB,OAAO;MAAEH,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAuB,CAAC;EAC1D;EAEA,IAAIC,SAAS,CAACC,MAAM,GAAG,GAAG,EAAE;IAC1B,OAAO;MAAEH,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAwC,CAAC;EAC3E;;EAEA;EACA,MAAMS,SAAS,GAAG,mBAAmB;EACrC,IAAI,CAACA,SAAS,CAACpB,IAAI,CAACY,SAAS,CAAC,EAAE;IAC9B,OAAO;MAAEF,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAmC,CAAC;EACtE;EAEA,OAAO;IAAED,OAAO,EAAE,IAAI;IAAEE;EAAU,CAAC;AACrC,CAAC;;AAED;AACA,OAAO,MAAMS,cAAc,GAAIC,MAAM,IAAK;EACxC,IAAI,CAACA,MAAM,EAAE;IACX,OAAO;MAAEZ,OAAO,EAAE,IAAI;MAAEE,SAAS,EAAE;IAAG,CAAC,CAAC,CAAC;EAC3C;EAEA,IAAI,OAAOU,MAAM,KAAK,QAAQ,EAAE;IAC9B,OAAO;MAAEZ,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAwB,CAAC;EAC3D;EAEA,MAAMC,SAAS,GAAGnB,cAAc,CAAC6B,MAAM,CAAC;EAExC,IAAIV,SAAS,CAACC,MAAM,GAAG,GAAG,EAAE;IAC1B,OAAO;MAAEH,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAA0C,CAAC;EAC7E;EAEA,OAAO;IAAED,OAAO,EAAE,IAAI;IAAEE;EAAU,CAAC;AACrC,CAAC;;AAED;AACA,OAAO,MAAMW,gBAAgB,GAAIJ,IAAI,IAAK;EACxC,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACrC,OAAO;MAAET,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAwB,CAAC;EAC3D;EAEA,MAAMC,SAAS,GAAGnB,cAAc,CAAC0B,IAAI,CAAC;EAEtC,IAAIP,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACxB,OAAO;MAAEH,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAA4B,CAAC;EAC/D;EAEA,IAAIC,SAAS,CAACC,MAAM,GAAG,EAAE,EAAE;IACzB,OAAO;MAAEH,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAA4C,CAAC;EAC/E;EAEA,OAAO;IAAED,OAAO,EAAE,IAAI;IAAEE;EAAU,CAAC;AACrC,CAAC;;AAED;AACA,OAAO,MAAMY,iBAAiB,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC1D,MAAMC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;EAE1B,OAAQC,UAAU,IAAK;IACrB,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,MAAME,WAAW,GAAGF,GAAG,GAAGJ,QAAQ;;IAElC;IACA,KAAK,MAAM,CAACO,GAAG,EAAEC,UAAU,CAAC,IAAIP,QAAQ,CAACQ,OAAO,CAAC,CAAC,EAAE;MAClD,MAAMC,eAAe,GAAGF,UAAU,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,GAAGN,WAAW,CAAC;MAC/D,IAAII,eAAe,CAACvB,MAAM,KAAK,CAAC,EAAE;QAChCc,QAAQ,CAACY,MAAM,CAACN,GAAG,CAAC;MACtB,CAAC,MAAM;QACLN,QAAQ,CAACa,GAAG,CAACP,GAAG,EAAEG,eAAe,CAAC;MACpC;IACF;;IAEA;IACA,MAAMK,YAAY,GAAGd,QAAQ,CAACe,GAAG,CAACb,UAAU,CAAC,IAAI,EAAE;IACnD,MAAMc,cAAc,GAAGF,YAAY,CAACJ,MAAM,CAACC,CAAC,IAAIA,CAAC,GAAGN,WAAW,CAAC;IAEhE,IAAIW,cAAc,CAAC9B,MAAM,IAAIY,WAAW,EAAE;MACxC,OAAO;QAAEmB,OAAO,EAAE,KAAK;QAAEC,SAAS,EAAEC,IAAI,CAACC,IAAI,CAAC,CAACJ,cAAc,CAAC,CAAC,CAAC,GAAGjB,QAAQ,GAAGI,GAAG,IAAI,IAAI;MAAE,CAAC;IAC9F;IAEAa,cAAc,CAACK,IAAI,CAAClB,GAAG,CAAC;IACxBH,QAAQ,CAACa,GAAG,CAACX,UAAU,EAAEc,cAAc,CAAC;IAExC,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}