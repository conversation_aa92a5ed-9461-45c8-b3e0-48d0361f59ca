import React, { useState, useEffect } from 'react';
import { collection, addDoc, deleteDoc, updateDoc, doc, onSnapshot, query, orderBy } from 'firebase/firestore';
import { Settings, Edit3, Trash2, ExternalLink } from 'lucide-react';
import { Section, InputField, ActionButton, SmallActionButton } from './UIComponents';
import ComponentErrorBoundary from './ComponentErrorBoundary';

function JobSites({ userId, db, appId }) {
  const [siteName, setSiteName] = useState('');
  const [siteUrl, setSiteUrl] = useState('');
  const [sites, setSites] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAdding, setIsAdding] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeletingSite, setIsDeletingSite] = useState(null);
  const [editingSiteId, setEditingSiteId] = useState(null);
  const [error, setError] = useState('');
  const [validationErrors, setValidationErrors] = useState({});

  const sitesCollectionRef = userId ? collection(db, `artifacts/${appId}/users/${userId}/jobSites`) : null;

  // Load sites
  useEffect(() => {
    if (!sitesCollectionRef) return;
    
    setIsLoading(true);
    const sitesQuery = query(sitesCollectionRef, orderBy('createdAt', 'desc'));

    const unsubscribe = onSnapshot(sitesQuery, (snapshot) => {
      const sitesData = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      setSites(sitesData);
      setIsLoading(false);
    }, (err) => {
      console.error("Error fetching sites: ", err);
      setError("Failed to load job sites: " + err.message);
      setIsLoading(false);
    });
    
    return () => unsubscribe();
  }, [sitesCollectionRef]);

  useEffect(() => {
    if (!userId) {
      setIsLoading(false);
    }
  }, [userId]);

  // Validate URL format
  const validateUrl = (url) => {
    // Basic URL validation regex
    const urlPattern = /^(https?:\/\/)?((([a-z\d]([a-z\d-]*[a-z\d])*)\.)+[a-z]{2,}|localhost)(:\d+)?(\/[-a-z\d%_.~+]*)*(\?[;&a-z\d%_.~+=-]*)?(#[-a-z\d_]*)?$/i;
    return urlPattern.test(url);
  };

  // Validate form input
  const validateForm = () => {
    const errors = {};
    let isValid = true;

    if (!siteName.trim()) {
      errors.name = "Site name is required";
      isValid = false;
    }

    if (!siteUrl.trim()) {
      errors.url = "URL is required";
      isValid = false;
    } else if (!validateUrl(siteUrl.trim())) {
      errors.url = "Please enter a valid URL";
      isValid = false;
    }

    setValidationErrors(errors);
    return isValid;
  };

  // Add or update job site
  const handleAddOrUpdateSite = async (e) => {
    e.preventDefault();
    setError('');
    
    if (!validateForm()) {
      return;
    }

    let url = siteUrl.trim();
    if (!url.startsWith('http')) {
      url = 'https://' + url;
    }

    if (editingSiteId) {
      setIsUpdating(true);
      try {
        const siteRef = doc(db, `artifacts/${appId}/users/${userId}/jobSites/${editingSiteId}`);
        await updateDoc(siteRef, {
          name: siteName.trim(),
          url: url,
          updatedAt: new Date()
        });
        handleCancelEdit();
      } catch (err) {
        setError("Error updating job site: " + err.message);
        console.error("Error updating site:", err);
      } finally {
        setIsUpdating(false);
      }
    } else {
      setIsAdding(true);
      try {
        await addDoc(sitesCollectionRef, {
          name: siteName.trim(),
          url: url,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        setSiteName('');
        setSiteUrl('');
        setValidationErrors({});
      } catch (err) {
        setError("Error adding job site: " + err.message);
        console.error("Error adding site:", err);
      } finally {
        setIsAdding(false);
      }
    }
  };

  // Edit a job site
  const handleEditSite = (site) => {
    setEditingSiteId(site.id);
    setSiteName(site.name);
    setSiteUrl(site.url);
    setError('');
    setValidationErrors({});
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingSiteId(null);
    setSiteName('');
    setSiteUrl('');
    setValidationErrors({});
  };

  // Delete a job site
  const handleDeleteSite = async (siteId) => {
    setIsDeletingSite(siteId);
    try {
      const siteRef = doc(db, `artifacts/${appId}/users/${userId}/jobSites/${siteId}`);
      await deleteDoc(siteRef);
    } catch (err) {
      setError("Error deleting job site: " + err.message);
      console.error("Error deleting site:", err);
    } finally {
      setIsDeletingSite(null);
    }
  };

  return (
    <Section title="Job Search Sites" icon={<Settings className="w-5 h-5" />}>
      {error && (
        <div className="mb-4 p-3 bg-red-500/20 border border-red-500/50 rounded-lg text-red-300 animate-fadeIn">
          <p>{error}</p>
        </div>
      )}
      
      <div className="mb-6 bg-slate-800/50 p-5 rounded-xl shadow-md">
        <h3 className="text-xl font-semibold mb-4 text-gradient-blue">
          {editingSiteId ? 'Edit Job Site' : 'Add New Job Site'}
        </h3>
        <form onSubmit={handleAddOrUpdateSite} className="space-y-4">
          <InputField 
            label="Site Name" 
            value={siteName} 
            onChange={(e) => {
              setSiteName(e.target.value);
              if (validationErrors.name) {
                setValidationErrors({...validationErrors, name: null});
              }
            }} 
            placeholder="LinkedIn" 
            error={validationErrors.name}
          />
          <InputField 
            label="Site URL" 
            value={siteUrl} 
            onChange={(e) => {
              setSiteUrl(e.target.value);
              if (validationErrors.url) {
                setValidationErrors({...validationErrors, url: null});
              }
            }} 
            placeholder="https://www.linkedin.com/jobs" 
            error={validationErrors.url}
          />
          <div className="pt-2 flex gap-2">
            <ActionButton 
              type="submit" 
              isLoading={editingSiteId ? isUpdating : isAdding} 
              loadingText={editingSiteId ? "Updating..." : "Adding..."}
            >
              {editingSiteId ? 'Update Site' : 'Add Site'}
            </ActionButton>
            
            {editingSiteId && (
              <ActionButton 
                type="button" 
                onClick={handleCancelEdit} 
                variant="secondary"
              >
                Cancel
              </ActionButton>
            )}
          </div>
        </form>
      </div>

      <div className="bg-slate-800/30 p-5 rounded-xl shadow-md">
        <h3 className="text-xl font-semibold mb-4 text-gradient-blue">Your Job Sites</h3>
        {isLoading ? (
          <div className="flex items-center justify-center p-10">
            <div className="animate-spin h-8 w-8 border-t-2 border-b-2 border-blue-500 rounded-full"></div>
          </div>
        ) : sites.length === 0 ? (
          <div className="p-8 text-center border border-dashed border-slate-700 rounded-lg bg-slate-800/30">
            <p className="text-slate-400">No job sites added yet</p>
            <p className="text-slate-500 text-sm mt-2">Add your favorite job search websites above to track them</p>
          </div>
        ) : (
          <ul className="space-y-3">
            {sites.map(site => (
              <li 
                key={site.id} 
                className="flex justify-between items-center p-4 bg-gradient-to-r from-slate-800 to-slate-800/80 rounded-lg border border-slate-700/50 hover:border-slate-600/50 transition-all shadow-sm hover:shadow-md"
              >
                <div>
                  <div className="font-medium text-slate-200">{site.name}</div>
                  <a 
                    href={site.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-400 text-sm hover:underline hover:text-blue-300 transition-colors flex items-center gap-1"
                  >
                    {site.url}
                    <ExternalLink size={12} />
                  </a>
                </div>
                <div className="flex space-x-2 shrink-0">
                  <SmallActionButton 
                    onClick={() => handleEditSite(site)} 
                    icon={<Edit3 size={14} />} 
                    aria-label="Edit site"
                    disabled={isDeletingSite === site.id}
                  />
                  <SmallActionButton 
                    onClick={() => handleDeleteSite(site.id)} 
                    icon={isDeletingSite === site.id ? (
                      <span className="animate-spin">
                        <svg className="h-3.5 w-3.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </span>
                    ) : <Trash2 size={14} />} 
                    variant="danger" 
                    aria-label="Delete site"
                    disabled={isDeletingSite === site.id}
                  />
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </Section>
  );
}

export default JobSites;
