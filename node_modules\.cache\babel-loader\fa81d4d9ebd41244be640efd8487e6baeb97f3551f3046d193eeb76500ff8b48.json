{"ast": null, "code": "/**\n * lucide-react v0.279.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst MapPinOff = createLucideIcon(\"MapPinOff\", [[\"path\", {\n  d: \"M5.43 5.43A8.06 8.06 0 0 0 4 10c0 6 8 12 8 12a29.94 29.94 0 0 0 5-5\",\n  key: \"12a8pk\"\n}], [\"path\", {\n  d: \"M19.18 13.52A8.66 8.66 0 0 0 20 10a8 8 0 0 0-8-8 7.88 7.88 0 0 0-3.52.82\",\n  key: \"1r9f6y\"\n}], [\"path\", {\n  d: \"M9.13 9.13A2.78 2.78 0 0 0 9 10a3 3 0 0 0 3 3 2.78 2.78 0 0 0 .87-.13\",\n  key: \"erynq7\"\n}], [\"path\", {\n  d: \"M14.9 9.25a3 3 0 0 0-2.15-2.16\",\n  key: \"1hwwmx\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a6p6uj\"\n}]]);\nexport { MapPinOff as default };", "map": {"version": 3, "names": ["MapPinOff", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\Documents\\Repo\\Auto_Job_Application\\node_modules\\lucide-react\\src\\icons\\map-pin-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MapPinOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNS40MyA1LjQzQTguMDYgOC4wNiAwIDAgMCA0IDEwYzAgNiA4IDEyIDggMTJhMjkuOTQgMjkuOTQgMCAwIDAgNS01IiAvPgogIDxwYXRoIGQ9Ik0xOS4xOCAxMy41MkE4LjY2IDguNjYgMCAwIDAgMjAgMTBhOCA4IDAgMCAwLTgtOCA3Ljg4IDcuODggMCAwIDAtMy41Mi44MiIgLz4KICA8cGF0aCBkPSJNOS4xMyA5LjEzQTIuNzggMi43OCAwIDAgMCA5IDEwYTMgMyAwIDAgMCAzIDMgMi43OCAyLjc4IDAgMCAwIC44Ny0uMTMiIC8+CiAgPHBhdGggZD0iTTE0LjkgOS4yNWEzIDMgMCAwIDAtMi4xNS0yLjE2IiAvPgogIDxsaW5lIHgxPSIyIiB4Mj0iMjIiIHkxPSIyIiB5Mj0iMjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/map-pin-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MapPinOff = createLucideIcon('MapPinOff', [\n  [\n    'path',\n    {\n      d: 'M5.43 5.43A8.06 8.06 0 0 0 4 10c0 6 8 12 8 12a29.94 29.94 0 0 0 5-5',\n      key: '12a8pk',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M19.18 13.52A8.66 8.66 0 0 0 20 10a8 8 0 0 0-8-8 7.88 7.88 0 0 0-3.52.82',\n      key: '1r9f6y',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M9.13 9.13A2.78 2.78 0 0 0 9 10a3 3 0 0 0 3 3 2.78 2.78 0 0 0 .87-.13',\n      key: 'erynq7',\n    },\n  ],\n  ['path', { d: 'M14.9 9.25a3 3 0 0 0-2.15-2.16', key: '1hwwmx' }],\n  ['line', { x1: '2', x2: '22', y1: '2', y2: '22', key: 'a6p6uj' }],\n]);\n\nexport default MapPinOff;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}