// Input validation and sanitization utilities

// Sanitize string input to prevent XSS
export const sanitizeString = (str) => {
  if (typeof str !== 'string') return '';
  
  return str
    .replace(/<script[^>]*>.*?<\/script>/gi, '') // Remove script tags
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers like onclick=
    .trim();
};

// Validate email format
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validate URL format
export const validateUrl = (url) => {
  try {
    const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
    return ['http:', 'https:'].includes(urlObj.protocol);
  } catch {
    return false;
  }
};

// Validate and sanitize search keywords
export const validateSearchKeywords = (keywords) => {
  if (!keywords || typeof keywords !== 'string') {
    return { isValid: false, error: 'Keywords are required' };
  }

  const sanitized = sanitizeString(keywords);

  if (sanitized.length < 2) {
    return { isValid: false, error: 'Keywords must be at least 2 characters long' };
  }

  if (sanitized.length > 100) {
    return { isValid: false, error: 'Keywords must be less than 100 characters' };
  }

  // Check for malicious patterns (more targeted to avoid blocking legitimate terms)
  const maliciousPatterns = [
    /<script[^>]*>/i,  // Script tags
    /javascript:/i,    // JavaScript protocol
    /data:text\/html/i, // Data URLs
    /vbscript:/i,      // VBScript protocol
    /on\w+\s*=/i       // Event handlers
  ];

  for (const pattern of maliciousPatterns) {
    if (pattern.test(sanitized)) {
      return { isValid: false, error: 'Keywords contain potentially harmful content' };
    }
  }

  return { isValid: true, sanitized };
};

// Validate location input
export const validateLocation = (location) => {
  if (!location) {
    return { isValid: true, sanitized: '' }; // Location is optional
  }

  if (typeof location !== 'string') {
    return { isValid: false, error: 'Invalid location format' };
  }

  const sanitized = sanitizeString(location);
  
  if (sanitized.length > 50) {
    return { isValid: false, error: 'Location must be less than 50 characters' };
  }

  return { isValid: true, sanitized };
};

// Validate name input
export const validateName = (name) => {
  if (!name || typeof name !== 'string') {
    return { isValid: false, error: 'Name is required' };
  }

  const sanitized = sanitizeString(name);
  
  if (sanitized.length < 1) {
    return { isValid: false, error: 'Name cannot be empty' };
  }
  
  if (sanitized.length > 100) {
    return { isValid: false, error: 'Name must be less than 100 characters' };
  }

  // Only allow letters, spaces, hyphens, and apostrophes
  const nameRegex = /^[a-zA-Z\s\-'.]+$/;
  if (!nameRegex.test(sanitized)) {
    return { isValid: false, error: 'Name contains invalid characters' };
  }

  return { isValid: true, sanitized };
};

// Validate skills input
export const validateSkills = (skills) => {
  if (!skills) {
    return { isValid: true, sanitized: '' }; // Skills are optional
  }

  if (typeof skills !== 'string') {
    return { isValid: false, error: 'Invalid skills format' };
  }

  const sanitized = sanitizeString(skills);
  
  if (sanitized.length > 500) {
    return { isValid: false, error: 'Skills must be less than 500 characters' };
  }

  return { isValid: true, sanitized };
};

// Validate job site name
export const validateSiteName = (name) => {
  if (!name || typeof name !== 'string') {
    return { isValid: false, error: 'Site name is required' };
  }

  const sanitized = sanitizeString(name);
  
  if (sanitized.length < 1) {
    return { isValid: false, error: 'Site name cannot be empty' };
  }
  
  if (sanitized.length > 50) {
    return { isValid: false, error: 'Site name must be less than 50 characters' };
  }

  return { isValid: true, sanitized };
};

// Rate limiting helper
export const createRateLimiter = (maxRequests, windowMs) => {
  const requests = new Map();
  
  return (identifier) => {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Clean old entries
    for (const [key, timestamps] of requests.entries()) {
      const validTimestamps = timestamps.filter(t => t > windowStart);
      if (validTimestamps.length === 0) {
        requests.delete(key);
      } else {
        requests.set(key, validTimestamps);
      }
    }
    
    // Check current identifier
    const userRequests = requests.get(identifier) || [];
    const recentRequests = userRequests.filter(t => t > windowStart);
    
    if (recentRequests.length >= maxRequests) {
      return { allowed: false, resetTime: Math.ceil((recentRequests[0] + windowMs - now) / 1000) };
    }
    
    recentRequests.push(now);
    requests.set(identifier, recentRequests);
    
    return { allowed: true };
  };
};
