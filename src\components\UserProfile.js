import React, { useState, useEffect } from 'react';
import { doc, setDoc, onSnapshot } from 'firebase/firestore';
import { User, Edit3, Save, PlusCircle, Trash2, Brain } from 'lucide-react';
import { Section, InputField, TextAreaField, ActionButton, InfoItem, LoadingSpinner } from './UIComponents';
import { generateProfileSummary } from '../services/aiService';
import ComponentErrorBoundary from './ComponentErrorBoundary';

function UserProfile({ userId, db, appId }) {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [keySkills, setKeySkills] = useState('');
  const [experiences, setExperiences] = useState([{ title: '', company: '', years: '', summary: '' }]);
  const [profileSummary, setProfileSummary] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSummaryLoading, setIsSummaryLoading] = useState(false);
  const [error, setError] = useState('');

  const profileDocRef = userId ? doc(db, `artifacts/${appId}/users/${userId}/profile/main`) : null;

  // Load profile data
  useEffect(() => {
    if (!profileDocRef) return;
    setIsLoading(true);
    const unsubscribe = onSnapshot(profileDocRef, (docSnap) => {
      if (docSnap.exists()) {
        const data = docSnap.data();
        setName(data.name || '');
        setEmail(data.email || '');
        setKeySkills(data.keySkills || '');
        setExperiences(data.experiences || [{ title: '', company: '', years: '', summary: '' }]);
        setProfileSummary(data.profileSummary || '');
        setIsEditing(false); // Default to view mode after loading
      } else {
        setIsEditing(true); // No data, start in edit mode
      }
      setIsLoading(false);
    }, (err) => {
      console.error("Error fetching profile: ", err);
      setError("Failed to load profile data.");
      setIsLoading(false);
    });
    return () => unsubscribe();
  }, [userId, profileDocRef, db, appId]);

  const handleSaveProfile = async () => {
    if (!profileDocRef) {
      setError("User not authenticated. Cannot save profile.");
      return;
    }
    setIsLoading(true);
    setError('');
    try {
      await setDoc(profileDocRef, { name, email, keySkills, experiences, profileSummary });
      setIsEditing(false);
    } catch (err) {
      console.error("Error saving profile: ", err);
      setError("Failed to save profile. Please try again.");
    }
    setIsLoading(false);
  };

  const handleGenerateSummary = async () => {
    if (!keySkills.trim() && experiences.every(exp => !exp.summary.trim())) {
      setError("Please enter some key skills or experience summaries to generate a profile summary.");
      return;
    }
    setIsSummaryLoading(true);
    setError('');

    try {
      const result = await generateProfileSummary(keySkills, experiences);
      if (result.success) {
        setProfileSummary(result.summary);
      } else {
        throw new Error(result.error || "Failed to generate summary");
      }
    } catch (err) {
      console.error("Error generating summary: ", err);
      setError(`Failed to generate summary: ${err.message}. Please try again.`);
    }
    setIsSummaryLoading(false);
  };

  const handleAddExperience = () => {
    setExperiences([...experiences, { title: '', company: '', years: '', summary: '' }]);
  };

  const handleRemoveExperience = (index) => {
    const newExperiences = experiences.filter((_, i) => i !== index);
    setExperiences(newExperiences);
  };

  const handleExperienceChange = (index, field, value) => {
    const newExperiences = experiences.map((exp, i) => 
      i === index ? { ...exp, [field]: value } : exp
    );
    setExperiences(newExperiences);
  };

  if (isLoading && !name && !email && !keySkills) { // Only show full loading if truly initial load
      return <Section title="Your Profile" icon={<User className="w-5 h-5" />}><LoadingSpinner /></Section>;
  }

  return (
    <ComponentErrorBoundary
      componentName="User Profile"
      errorMessage="There was an error with the profile functionality."
    >
      <Section title="Your Profile" icon={<User className="w-5 h-5" />}>
      {error && <div className="mb-4 p-3 bg-red-500/20 text-red-300 border border-red-500 rounded-md">{error}</div>}
      
      {!isEditing ? (
        <div className="space-y-4">
          <InfoItem label="Name" value={name || "Not set"} />
          <InfoItem label="Email" value={email || "Not set"} />
          <InfoItem label="Key Skills" value={keySkills || "Not set"} isPreformatted={true} />
          
          <h3 className="text-lg font-semibold text-sky-400 mt-6 mb-2">Experiences:</h3>
          {experiences.length > 0 && experiences.some(exp => exp.title || exp.company || exp.summary) ? (
            experiences.map((exp, index) => (
              <div key={index} className="p-3 bg-slate-700/50 rounded-md mb-3">
                <p className="font-semibold text-slate-200">{exp.title || "N/A"} at {exp.company || "N/A"} ({exp.years || "N/A"} yrs)</p>
                <p className="text-sm text-slate-400 whitespace-pre-wrap">{exp.summary || "No summary."}</p>
              </div>
            ))
          ) : <p className="text-slate-500 italic">No experiences added.</p>}

          <h3 className="text-lg font-semibold text-sky-400 mt-6 mb-2">AI Generated Profile Summary:</h3>
          {profileSummary ? (
            <p className="text-slate-300 bg-slate-700/50 p-3 rounded-md whitespace-pre-wrap">{profileSummary}</p>
          ) : (
            <p className="text-slate-500 italic">No summary generated yet. Edit your profile and click "Generate Summary".</p>
          )}
          <ActionButton onClick={() => setIsEditing(true)} icon={<Edit3 />} disabled={isLoading}>
            Edit Profile
          </ActionButton>
        </div>
      ) : (
        <div className="space-y-4">
          <InputField label="Name" type="text" value={name} onChange={(e) => setName(e.target.value)} placeholder="Your Full Name" />
          <InputField label="Email" type="email" value={email} onChange={(e) => setEmail(e.target.value)} placeholder="<EMAIL>" />
          <TextAreaField label="Key Skills" value={keySkills} onChange={(e) => setKeySkills(e.target.value)} placeholder="e.g., React, Node.js, Project Management, Communication" rows={4} />

          <h3 className="text-lg font-semibold text-sky-400 mt-4 mb-2">Experiences:</h3>
          {experiences.map((exp, index) => (
            <div key={index} className="p-4 border border-slate-700 rounded-lg space-y-3 mb-3 relative">
              <InputField label={`Position ${index + 1}`} type="text" value={exp.title} onChange={(e) => handleExperienceChange(index, 'title', e.target.value)} placeholder="Job Title" />
              <InputField label="Company" type="text" value={exp.company} onChange={(e) => handleExperienceChange(index, 'company', e.target.value)} placeholder="Company Name" />
              <InputField label="Years" type="text" value={exp.years} onChange={(e) => handleExperienceChange(index, 'years', e.target.value)} placeholder="e.g., 3 or 2020-2023" />
              <TextAreaField label="Summary / Achievements" value={exp.summary} onChange={(e) => handleExperienceChange(index, 'summary', e.target.value)} placeholder="Brief summary of your role and achievements" rows={3} />
              {experiences.length > 1 && (
                <button 
                  type="button" 
                  onClick={() => handleRemoveExperience(index)}
                  className="absolute top-2 right-2 text-red-400 hover:text-red-300 transition-colors"
                  aria-label="Remove experience"
                >
                  <Trash2 size={18} />
                </button>
              )}
            </div>
          ))}
          <ActionButton onClick={handleAddExperience} icon={<PlusCircle />} variant="secondary" className="mt-2">
            Add Experience
          </ActionButton>

          <div className="mt-6 flex flex-col sm:flex-row gap-3">
            <ActionButton onClick={handleSaveProfile} icon={<Save />} disabled={isLoading || isSummaryLoading} className="flex-1">
              {isLoading ? 'Saving...' : 'Save Profile'}
            </ActionButton>
            <ActionButton onClick={handleGenerateSummary} icon={<Brain />} disabled={isSummaryLoading || isLoading} variant="secondary" className="flex-1">
              {isSummaryLoading ? 'Generating...' : (profileSummary ? 'Regenerate Summary (AI)' : 'Generate Summary (AI)')}
            </ActionButton>
          </div>
          {profileSummary && isEditing && (
             <div className="mt-4 p-3 bg-slate-700/30 rounded-md">
                <p className="text-sm text-sky-300 mb-1">Current AI Summary (will be saved with profile):</p>
                <p className="text-slate-400 text-xs whitespace-pre-wrap">{profileSummary}</p>
             </div>
          )}
        </div>
      )}
    </Section>
    </ComponentErrorBoundary>
  );
}

export default UserProfile;
